package com.example.springboot.tool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.linear.LUDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import com.example.springboot.tool.ReverseDataAdjustmentTools.AdjustmentResult;
import com.example.springboot.entity.SurveyData;
import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.service.AIChatService;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 逆向数据调整工具类高级功能
 * 包含中介效应、调节效应、因子分析等高级调整功能
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReverseDataAdjustmentToolsAdvanced {

    private final ReverseDataAdjustmentTools baseTools;
    private final AIChatService aiChatService;
    private final Random random = new Random();

    // 缓存题目量表级数信息，避免重复查询
    private Map<Integer, Integer> questionScaleLevelsCache = new HashMap<>();

    // 当前使用的量表级数
    private Integer currentScaleLevel = 5;

    /**
     * 调整数据以符合中介效应模型
     */
    @Tool(description = "调整数据以符合指定的中介效应模型，支持平行中介和链式中介")
    public String adjustDataForMediationEffect(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars,
            @ToolParam(description = "中介变量列索引列表（从1开始）") List<Integer> mediatorVars,
            @ToolParam(description = "中介类型：parallel（平行中介）或chain（链式中介）") String mediationType,
            @ToolParam(description = "目标直接效应系数") Double targetDirectEffect,
            @ToolParam(description = "目标间接效应系数") Double targetIndirectEffect,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[中介效应调整] 开始调整数据，sessionId={}, mediationType={}, targetDirect={}, targetIndirect={}",
                sessionId, mediationType, targetDirectEffect, targetIndirectEffect);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<Double> yData = baseTools.getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                xData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }
            List<List<Double>> mData = new ArrayList<>();
            for (Integer col : mediatorVars) {
                mData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }

            if (yData.isEmpty() || xData.isEmpty() || mData.isEmpty()) {
                throw new RuntimeException("数据为空");
            }

            List<List<Object>> changedCells = new ArrayList<>();

            if ("parallel".equals(mediationType)) {
                // 平行中介效应调整
                changedCells = adjustParallelMediationEffect(sessionId, yData, xData, mData,
                    dependentVar, independentVars, mediatorVars, targetDirectEffect, targetIndirectEffect);
            } else if ("chain".equals(mediationType)) {
                // 链式中介效应调整
                changedCells = adjustChainMediationEffect(sessionId, yData, xData, mData,
                    dependentVar, independentVars, mediatorVars, targetDirectEffect, targetIndirectEffect);
            } else {
                throw new IllegalArgumentException("不支持的中介类型：" + mediationType);
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("mediationType", mediationType);
            metrics.put("targetDirectEffect", targetDirectEffect);
            metrics.put("targetIndirectEffect", targetIndirectEffect);
            metrics.put("cellsChanged", changedCells.size());

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整中介效应模型，目标直接效应%.3f，间接效应%.3f，共调整%d个单元格。",
                targetDirectEffect, targetIndirectEffect, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[中介效应调整] 调整失败", e);
            return "中介效应调整失败: " + e.getMessage();
        }
    }

    /**
     * 调整平行中介效应
     */
    private List<List<Object>> adjustParallelMediationEffect(String sessionId, List<Double> yData,
            List<List<Double>> xData, List<List<Double>> mData, Integer dependentVar,
            List<Integer> independentVars, List<Integer> mediatorVars,
            Double targetDirectEffect, Double targetIndirectEffect) {

        List<List<Object>> changedCells = new ArrayList<>();
        int sampleSize = yData.size();

        // 调整中介变量以产生目标间接效应
        for (int i = 0; i < mediatorVars.size(); i++) {
            List<Double> adjustedMediatorData = generateMediatorData(xData.get(0), targetIndirectEffect / mediatorVars.size());

            // 生成变更记录
            for (int j = 0; j < sampleSize; j++) {
                if (!mData.get(i).get(j).equals(adjustedMediatorData.get(j))) {
                    double adjustedValue = adjustedMediatorData.get(j);
                    // 根据中介变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(mediatorVars.get(i), mData.get(i));
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        j + 1,
                        mediatorVars.get(i) - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }
        }

        // 调整因变量以产生目标直接效应
        List<Double> adjustedYData = generateDependentVariableWithMediationEffect(
            xData, mData, targetDirectEffect, targetIndirectEffect);

        for (int i = 0; i < sampleSize; i++) {
            if (!yData.get(i).equals(adjustedYData.get(i))) {
                double adjustedValue = adjustedYData.get(i);
                // 根据因变量的量表级数动态确定范围
                Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                changedCells.add(Arrays.asList(
                    i + 1,
                    dependentVar - 1,
                    String.valueOf(optionValue)
                ));
            }
        }

        return changedCells;
    }

    /**
     * 调整链式中介效应
     */
    private List<List<Object>> adjustChainMediationEffect(String sessionId, List<Double> yData,
            List<List<Double>> xData, List<List<Double>> mData, Integer dependentVar,
            List<Integer> independentVars, List<Integer> mediatorVars,
            Double targetDirectEffect, Double targetIndirectEffect) {

        List<List<Object>> changedCells = new ArrayList<>();
        int sampleSize = yData.size();

        // 链式中介：X -> M1 -> M2 -> ... -> Y
        List<List<Double>> adjustedMediators = new ArrayList<>();

        // 第一个中介变量基于自变量调整
        List<Double> firstMediator = generateMediatorData(xData.get(0), 0.5);
        adjustedMediators.add(firstMediator);

        // 后续中介变量基于前一个中介变量调整
        for (int i = 1; i < mediatorVars.size(); i++) {
            List<Double> nextMediator = generateMediatorData(adjustedMediators.get(i-1), 0.5);
            adjustedMediators.add(nextMediator);
        }

        // 生成中介变量的变更记录
        for (int i = 0; i < mediatorVars.size(); i++) {
            for (int j = 0; j < sampleSize; j++) {
                if (!mData.get(i).get(j).equals(adjustedMediators.get(i).get(j))) {
                    double adjustedValue = adjustedMediators.get(i).get(j);
                    // 根据中介变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(mediatorVars.get(i), mData.get(i));
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        j + 1,
                        mediatorVars.get(i) - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }
        }

        // 调整因变量
        List<Double> adjustedYData = generateDependentVariableWithChainMediation(
            xData, adjustedMediators, targetDirectEffect, targetIndirectEffect);

        for (int i = 0; i < sampleSize; i++) {
            if (!yData.get(i).equals(adjustedYData.get(i))) {
                double adjustedValue = adjustedYData.get(i);
                // 根据因变量的量表级数动态确定范围
                Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                changedCells.add(Arrays.asList(
                    i + 1,
                    dependentVar - 1,
                    String.valueOf(optionValue)
                ));
            }
        }

        return changedCells;
    }

    /**
     * 生成中介变量数据
     */
    private List<Double> generateMediatorData(List<Double> predictor, double effectSize) {
        List<Double> result = new ArrayList<>();
        NormalDistribution errorDist = new NormalDistribution(0, 0.5);

        DescriptiveStatistics stats = new DescriptiveStatistics();
        predictor.forEach(stats::addValue);
        double mean = stats.getMean();
        double std = stats.getStandardDeviation();

        for (Double x : predictor) {
            double standardizedX = (x - mean) / std;
            double mediatorValue = effectSize * standardizedX + errorDist.sample();
            result.add(mediatorValue);
        }

        return result;
    }

    /**
     * 生成具有中介效应的因变量数据
     */
    private List<Double> generateDependentVariableWithMediationEffect(List<List<Double>> xData,
            List<List<Double>> mData, double directEffect, double indirectEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 直接效应
            yValue += directEffect * xData.get(0).get(i);

            // 间接效应（通过中介变量）
            for (List<Double> mediator : mData) {
                yValue += (indirectEffect / mData.size()) * mediator.get(i);
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 生成具有链式中介效应的因变量数据
     */
    private List<Double> generateDependentVariableWithChainMediation(List<List<Double>> xData,
            List<List<Double>> mData, double directEffect, double indirectEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 直接效应
            yValue += directEffect * xData.get(0).get(i);

            // 链式间接效应（通过最后一个中介变量）
            if (!mData.isEmpty()) {
                yValue += indirectEffect * mData.get(mData.size() - 1).get(i);
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 调整数据以符合调节效应模型
     */
    @Tool(description = "调整数据以符合指定的调节效应模型，控制交互项的显著性")
    public String adjustDataForModerationEffect(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars,
            @ToolParam(description = "调节变量列索引列表（从1开始）") List<Integer> moderatorVars,
            @ToolParam(description = "目标主效应系数列表") List<Double> targetMainEffects,
            @ToolParam(description = "目标交互效应系数") Double targetInteractionEffect,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[调节效应调整] 开始调整数据，sessionId={}, dependentVar={}, targetInteraction={}",
                sessionId, dependentVar, targetInteractionEffect);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<Double> yData = baseTools.getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xData = new ArrayList<>();
            for (Integer col : independentVars) {
                xData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }
            List<List<Double>> zData = new ArrayList<>();
            for (Integer col : moderatorVars) {
                zData.add(baseTools.getNumericColumnData(sessionId, col - 1));
            }

            if (yData.isEmpty() || xData.isEmpty() || zData.isEmpty()) {
                throw new RuntimeException("数据为空");
            }

            // 验证参数
            if (targetMainEffects.size() != independentVars.size() + moderatorVars.size()) {
                throw new IllegalArgumentException("主效应系数数量必须等于自变量和调节变量的总数");
            }

            // 生成具有调节效应的因变量数据
            List<Double> adjustedYData = generateModerationEffectData(xData, zData,
                targetMainEffects, targetInteractionEffect);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int i = 0; i < yData.size(); i++) {
                if (!yData.get(i).equals(adjustedYData.get(i))) {
                    double adjustedValue = adjustedYData.get(i);
                    // 根据因变量的量表级数动态确定范围
                    Integer scaleLevel = getQuestionScaleLevel(dependentVar, yData);
                    int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                    changedCells.add(Arrays.asList(
                        i + 1,
                        dependentVar - 1,
                        String.valueOf(optionValue)
                    ));
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetMainEffects", targetMainEffects);
            metrics.put("targetInteractionEffect", targetInteractionEffect);
            metrics.put("cellsChanged", changedCells.size());

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整调节效应模型，目标交互效应%.3f，共调整%d个单元格。",
                targetInteractionEffect, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[调节效应调整] 调整失败", e);
            return "调节效应调整失败: " + e.getMessage();
        }
    }

    /**
     * 生成具有调节效应的数据
     */
    private List<Double> generateModerationEffectData(List<List<Double>> xData, List<List<Double>> zData,
            List<Double> mainEffects, double interactionEffect) {

        List<Double> result = new ArrayList<>();
        int sampleSize = xData.get(0).size();
        NormalDistribution errorDist = new NormalDistribution(0, 0.3);

        for (int i = 0; i < sampleSize; i++) {
            double yValue = 0.0;

            // 主效应 - 自变量
            for (int j = 0; j < xData.size(); j++) {
                yValue += mainEffects.get(j) * xData.get(j).get(i);
            }

            // 主效应 - 调节变量
            for (int j = 0; j < zData.size(); j++) {
                yValue += mainEffects.get(xData.size() + j) * zData.get(j).get(i);
            }

            // 交互效应 (X * Z)
            for (int j = 0; j < xData.size(); j++) {
                for (int k = 0; k < zData.size(); k++) {
                    yValue += interactionEffect * xData.get(j).get(i) * zData.get(k).get(i);
                }
            }

            // 添加误差项
            yValue += errorDist.sample();

            result.add(yValue);
        }

        return result;
    }

    /**
     * 调整数据以改善因子分析结果
     */
    @Tool(description = "调整数据以改善因子分析结果，提高KMO值和因子载荷")
    public String adjustDataForFactorAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要调整的列索引列表（从1开始）") List<Integer> columns,
            @ToolParam(description = "目标KMO值（0.5-1.0）") Double targetKMO,
            @ToolParam(description = "目标因子载荷阈值（如0.6）") Double targetLoadingThreshold,
            @ToolParam(description = "允许的误差范围，默认0.05") Double tolerance) {

        log.info("[因子分析调整] 开始调整数据，sessionId={}, columns={}, targetKMO={}",
                sessionId, columns, targetKMO);

        try {
            if (tolerance == null) tolerance = 0.05;

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 检查数据有效性，特别是同值问题
            if (!isDataValid(originalData)) {
                log.error("[因子分析调整] 原始数据无效，可能存在同值列，先进行数据修复");
                originalData = ensureDataVariability(originalData);
            }

            // 增强变量间相关性以提高KMO值
            List<List<Double>> adjustedData = enhanceCorrelationsForFactorAnalysis(originalData, targetKMO);

            // 最终确保数据变异性
            adjustedData = ensureDataVariability(adjustedData);

            // 生成变更记录
            List<List<Object>> changedCells = new ArrayList<>();
            for (int colIdx = 0; colIdx < columns.size(); colIdx++) {
                List<Double> originalCol = originalData.get(colIdx);
                List<Double> adjustedCol = adjustedData.get(colIdx);

                for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                    if (!originalCol.get(rowIdx).equals(adjustedCol.get(rowIdx))) {
                        double adjustedValue = adjustedCol.get(rowIdx);
                        // 根据列的量表级数动态确定范围
                        Integer scaleLevel = getQuestionScaleLevel(columns.get(colIdx), originalCol);
                        int optionValue = Math.max(1, Math.min(scaleLevel, (int) Math.round(adjustedValue)));

                        changedCells.add(Arrays.asList(
                            rowIdx + 1,
                            columns.get(colIdx) - 1,
                            String.valueOf(optionValue)
                        ));
                    }
                }
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("targetKMO", targetKMO);
            metrics.put("targetLoadingThreshold", targetLoadingThreshold);
            metrics.put("cellsChanged", changedCells.size());

            // 重新计算实际的KMO值，确保信息一致性
            Map<String, Object> actualStats = baseTools.calculateFactorAnalysisInternal(sessionId, columns);
            double actualKMO = (Double) actualStats.getOrDefault("kmo", targetKMO);
            metrics.put("achievedKMO", actualKMO);

            // 生成调整说明，但不保存到数据库（让AI聊天服务统一处理）
            String explanation = String.format(
                "成功调整因子分析KMO值到%.3f（目标%.3f），载荷阈值%.2f，共调整%d个单元格。",
                actualKMO, targetKMO, targetLoadingThreshold, changedCells.size()
            );

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, metrics);

        } catch (Exception e) {
            log.error("[因子分析调整] 调整失败", e);
            return "因子分析调整失败: " + e.getMessage();
        }
    }

    /**
     * 增强变量间相关性以改善因子分析 - 保守版本
     * 防止过度调整导致同值问题
     */
    private List<List<Double>> enhanceCorrelationsForFactorAnalysis(List<List<Double>> data, double targetKMO) {
        List<List<Double>> result = new ArrayList<>();
        int numVars = data.size();
        int sampleSize = data.get(0).size();

        log.info("[因子分析增强] 开始增强相关性，目标KMO: {:.3f}", targetKMO);

        // 保存原始分布特征
        List<Double> originalStds = new ArrayList<>();
        for (List<Double> col : data) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            originalStds.add(stats.getStandardDeviation());
        }

        // 保守的相关性增强策略
        for (int i = 0; i < numVars; i++) {
            List<Double> adjustedVar = new ArrayList<>();
            List<Double> originalVar = data.get(i);

            for (int j = 0; j < sampleSize; j++) {
                double originalValue = originalVar.get(j);

                // 只对部分数据点进行调整
                if (Math.random() > 0.3) { // 只调整30%的数据点
                    adjustedVar.add(originalValue);
                    continue;
                }

                double adjustment = 0.0;

                // 基于其他变量的加权平均进行调整
                for (int k = 0; k < numVars; k++) {
                    if (k != i) {
                        adjustment += data.get(k).get(j);
                    }
                }
                adjustment /= (numVars - 1);

                // 向共同因子方向轻微调整，增强相关性
                double enhancementFactor = 0.1; // 减小调整强度，防止过度调整
                double adjustedValue = originalValue + enhancementFactor * (adjustment - originalValue);

                // 使用默认的7级量表范围（支持大多数量表类型）
                adjustedValue = Math.max(1.0, Math.min(7.0, adjustedValue));
                adjustedVar.add(adjustedValue);
            }

            result.add(adjustedVar);
        }

        // 检查调整后的分布是否被过度破坏
        for (int i = 0; i < numVars; i++) {
            DescriptiveStatistics newStats = new DescriptiveStatistics();
            result.get(i).forEach(newStats::addValue);

            double newStd = newStats.getStandardDeviation();
            double originalStd = originalStds.get(i);

            if (newStd < originalStd * 0.5) {
                log.warn("[因子分析增强] 第{}列标准差过度减小({:.3f} -> {:.3f})，恢复部分原始数据",
                        i + 1, originalStd, newStd);

                // 恢复一些原始数据点
                List<Double> originalCol = data.get(i);
                List<Double> adjustedCol = result.get(i);

                for (int j = 0; j < sampleSize; j++) {
                    if (Math.random() < 0.4) { // 恢复40%的数据点
                        adjustedCol.set(j, originalCol.get(j));
                    }
                }
            }
        }

        log.info("[因子分析增强] 完成相关性增强");
        return result;
    }

    /**
     * 分维度量表信度效度调整工具
     * 支持用户指定哪几题是一个维度，确保每个维度的信效度可以通过，并且总量表的信度也可以通过
     * 支持设置每个维度题目的得分均值和得分方向
     *
     * 使用示例：
     * 1. 基本用法：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, null, null)
     * 2. 设置题目均值：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, [[3.5,3.2,3.8],[4.0,3.9,4.1]], null)
     * 3. 设置得分方向：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], null, null, null, null, null, null, [["positive","negative","positive"],["positive","positive","negative"]])
     * 4. 完整设置：adjustDataForMultiDimensionalScale(sessionId, [[1,2,3],[4,5,6]], [0.8,0.85], 0.9, 0.8, 0.4, 0.02, [[3.5,3.2,3.8],[4.0,3.9,4.1]], [["positive","negative","positive"],["positive","positive","negative"]])
     */
    @Tool(description = "调整分维度量表数据，确保每个维度的信度和效度都能通过，同时保证总量表的信度也能通过。支持设置题目得分均值和得分方向。除了dimensions和scaleLevel参数必须指定外，其他参数都有合理的默认值")
    public String adjustDataForMultiDimensionalScale(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "维度定义，每个维度包含的题目列号列表，格式：[[1,2,3],[4,5,6],[7,8,9]]") List<List<Integer>> dimensions,
            @ToolParam(description = "量表级数，如5表示5级量表(1-5)，7表示7级量表(1-7)") Integer scaleLevel,
            @ToolParam(description = "每个维度的目标信度系数列表，与dimensions对应，不指定时默认为0.8") List<Double> targetDimensionAlphas,
            @ToolParam(description = "总量表的目标信度系数，不指定时默认为0.85") Double targetTotalAlpha,
            @ToolParam(description = "目标KMO值（效度指标，0.6-1.0），不指定时默认为0.8") Double targetKMO,
            @ToolParam(description = "维度间目标相关系数（用于区分效度），不指定时默认为0.4") Double targetInterDimensionCorrelation,
            @ToolParam(description = "允许的误差范围，默认0.02") Double tolerance,
            @ToolParam(description = "每个维度题目的目标得分均值列表，与dimensions对应，格式：[[3.5,3.2,3.8],[4.0,3.9,4.1]]，不指定时不调整均值") List<List<Double>> targetItemMeans,
            @ToolParam(description = "每个维度题目的得分方向列表，与dimensions对应，格式：[['positive','negative','positive'],['positive','positive','negative']]，'positive'表示正向计分（1分最低），'negative'表示反向计分（1分最高），不指定时默认全部为正向") List<List<String>> scoringDirections) {

        log.info("[分维度量表调整] 开始调整数据，sessionId={}, dimensions={}, scaleLevel={}, targetTotalAlpha={}",
                sessionId, dimensions, scaleLevel, targetTotalAlpha);

        try {
            // ========== 第一阶段：参数验证和默认值设置 ==========
            if (tolerance == null) tolerance = 0.02;
            if (targetTotalAlpha == null) targetTotalAlpha = 0.85;
            if (targetKMO == null) targetKMO = 0.8;
            if (targetInterDimensionCorrelation == null) targetInterDimensionCorrelation = 0.4;

            // 验证参数
            if (dimensions == null || dimensions.isEmpty()) {
                throw new IllegalArgumentException("维度定义不能为空，必须指定每个维度包含的题目");
            }

            if (scaleLevel == null || scaleLevel < 2 || scaleLevel > 10) {
                throw new IllegalArgumentException("量表级数必须在2到10之间，当前值：" + scaleLevel);
            }

            // 设置当前使用的量表级数
            this.currentScaleLevel = scaleLevel;

            // 验证数值参数范围
            if (targetTotalAlpha <= 0 || targetTotalAlpha > 1) {
                throw new IllegalArgumentException("总信度系数必须在0到1之间，当前值：" + targetTotalAlpha);
            }
            if (targetTotalAlpha < 0.6) {
                throw new IllegalArgumentException("总信度系数过低（" + targetTotalAlpha + "），建议设置在0.6以上");
            }

            if (targetKMO <= 0 || targetKMO > 1) {
                throw new IllegalArgumentException("KMO值必须在0到1之间，当前值：" + targetKMO);
            }
            if (targetKMO < 0.5) {
                throw new IllegalArgumentException("KMO值过低（" + targetKMO + "），建议设置在0.5以上");
            }

            if (targetInterDimensionCorrelation < 0 || targetInterDimensionCorrelation > 1) {
                throw new IllegalArgumentException("维度间相关系数必须在0到1之间，当前值：" + targetInterDimensionCorrelation);
            }

            if (tolerance <= 0 || tolerance > 0.5) {
                throw new IllegalArgumentException("容差必须在0到0.5之间，当前值：" + tolerance);
            }

            // 为每个维度设置默认信度目标值
            if (targetDimensionAlphas == null || targetDimensionAlphas.isEmpty()) {
                targetDimensionAlphas = new ArrayList<>();
                for (int i = 0; i < dimensions.size(); i++) {
                    targetDimensionAlphas.add(0.8); // 默认每个维度目标信度为0.8
                }
                log.info("[分维度量表调整] 使用默认维度信度目标值: {}", targetDimensionAlphas);
            } else if (targetDimensionAlphas.size() != dimensions.size()) {
                throw new IllegalArgumentException("维度目标信度数量必须与维度数量一致，或者不指定使用默认值0.8");
            }

            // 验证每个维度的信度系数
            for (int i = 0; i < targetDimensionAlphas.size(); i++) {
                Double alpha = targetDimensionAlphas.get(i);
                if (alpha == null || alpha <= 0 || alpha > 1) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "的信度系数必须在0到1之间，当前值：" + alpha);
                }
                if (alpha < 0.5) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "的信度系数过低（" + alpha + "），建议设置在0.6以上");
                }
            }

            // 验证维度定义和题目类型
            Set<Integer> allQuestions = new HashSet<>();
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                if (dimension == null || dimension.isEmpty()) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "不能为空");
                }
                if (dimension.size() < 2) {
                    throw new IllegalArgumentException("维度" + (i + 1) + "至少需要2个题目，当前只有" + dimension.size() + "个");
                }
                for (Integer questionNum : dimension) {
                    if (questionNum == null || questionNum <= 0) {
                        throw new IllegalArgumentException("维度" + (i + 1) + "中包含无效的题目编号: " + questionNum);
                    }
                    if (allQuestions.contains(questionNum)) {
                        throw new IllegalArgumentException("题目" + questionNum + "被重复分配到多个维度");
                    }
                    allQuestions.add(questionNum);
                }
            }

            // 获取题目结构信息，用于动态确定量表级数和验证题目类型
            questionScaleLevelsCache.clear(); // 清空缓存
            Map<Integer, String> questionTypesCache = new HashMap<>();
            Map<Integer, Integer> questionScaleLevelsMap = new HashMap<>();
            Map<Integer, Integer> columnToQuestionMap = new HashMap<>(); // 列编号到题目编号的映射
            Map<Integer, SurveyData> questionDataMap = new HashMap<>(); // 题目编号到题目数据的映射

            try {
                WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
                if (surveyData != null && surveyData.getJsonData() != null) {
                    for (SurveyData data : surveyData.getJsonData()) {
                        questionTypesCache.put(data.getNumId(), data.getType());
                        questionDataMap.put(data.getNumId(), data);

                        // 建立列编号到题目编号的映射
                        if (data.getColIndices() != null && !data.getColIndices().isEmpty()) {
                            for (Integer colIndex : data.getColIndices()) {
                                columnToQuestionMap.put(colIndex, data.getNumId());
                            }
                        }

                        if (data.getOptions() != null && !data.getOptions().isEmpty()) {
                            int detectedScaleLevel = data.getOptions().size();
                            questionScaleLevelsCache.put(data.getNumId(), detectedScaleLevel);
                            questionScaleLevelsMap.put(data.getNumId(), detectedScaleLevel);
                        } else if (data.getSubQuestions() != null && !data.getSubQuestions().isEmpty()) {
                            // 对于矩阵题，使用第一个子问题的选项数量
                            SurveyData.SubQuestion firstSubQ = data.getSubQuestions().get(0);
                            if (firstSubQ != null && firstSubQ.getOptions() != null && !firstSubQ.getOptions().isEmpty()) {
                                int detectedScaleLevel = firstSubQ.getOptions().size();
                                questionScaleLevelsCache.put(data.getNumId(), detectedScaleLevel);
                                questionScaleLevelsMap.put(data.getNumId(), detectedScaleLevel);
                            }
                        }
                    }
                }
                log.info("[分维度量表调整] 获取到的题目量表级数信息: {}", questionScaleLevelsCache);
                log.info("[分维度量表调整] 获取到的题目类型信息: {}", questionTypesCache);
                log.info("[分维度量表调整] 列编号到题目编号映射: {}", columnToQuestionMap);
            } catch (Exception e) {
                log.warn("[分维度量表调整] 获取题目结构信息失败，将使用默认验证范围: {}", e.getMessage());
            }

            // 将列编号转换为题目编号进行验证
            Set<Integer> actualQuestions = new HashSet<>();
            for (Integer columnNum : allQuestions) {
                Integer questionNum = columnToQuestionMap.get(columnNum);
                if (questionNum != null) {
                    actualQuestions.add(questionNum);
                } else {
                    log.warn("[分维度量表调整] 列编号{}没有对应的题目，跳过验证", columnNum);
                }
            }

            // 验证题目类型 - 分维度调整只支持特定题型
            validateQuestionTypesForMultiDimensional(actualQuestions, questionTypesCache);

            // 验证量表级数一致性
            validateScaleConsistency(actualQuestions, questionScaleLevelsMap);

            // 验证题目得分均值参数
            if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
                if (targetItemMeans.size() != dimensions.size()) {
                    throw new IllegalArgumentException("题目得分均值列表数量必须与维度数量一致");
                }
                for (int i = 0; i < dimensions.size(); i++) {
                    if (targetItemMeans.get(i).size() != dimensions.get(i).size()) {
                        throw new IllegalArgumentException(String.format("维度%d的题目得分均值数量必须与该维度题目数量一致", i + 1));
                    }
                    // 验证每个题目的目标均值 - 根据实际量表级数动态验证
                    for (int j = 0; j < targetItemMeans.get(i).size(); j++) {
                        Double mean = targetItemMeans.get(i).get(j);
                        if (mean != null) {
                            Integer questionNum = dimensions.get(i).get(j);
                            Integer cachedScaleLevel = questionScaleLevelsCache.get(questionNum);

                            // 如果无法获取量表级数，使用传入的参数
                            int maxLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                            if (mean < 1 || mean > maxLevel) {
                                throw new IllegalArgumentException(String.format("维度%d题目%d的目标均值应在1到%d之间（%d级量表），当前值：%.2f",
                                    i + 1, j + 1, maxLevel, maxLevel, mean));
                            }
                        }
                    }
                }
                log.info("[分维度量表调整] 使用指定的题目得分均值: {}", targetItemMeans);
            }

            // 验证得分方向参数
            if (scoringDirections != null && !scoringDirections.isEmpty()) {
                if (scoringDirections.size() != dimensions.size()) {
                    throw new IllegalArgumentException("得分方向列表数量必须与维度数量一致");
                }
                for (int i = 0; i < dimensions.size(); i++) {
                    if (scoringDirections.get(i).size() != dimensions.get(i).size()) {
                        throw new IllegalArgumentException(String.format("维度%d的得分方向数量必须与该维度题目数量一致", i + 1));
                    }
                    // 验证得分方向值
                    for (String direction : scoringDirections.get(i)) {
                        if (!"positive".equals(direction) && !"negative".equals(direction)) {
                            throw new IllegalArgumentException("得分方向只能是'positive'或'negative'");
                        }
                    }
                }
                log.info("[分维度量表调整] 使用指定的得分方向: {}", scoringDirections);
            } else {
                // 设置默认得分方向（全部为正向）
                scoringDirections = new ArrayList<>();
                for (List<Integer> dimension : dimensions) {
                    List<String> dimensionDirections = new ArrayList<>();
                    for (int j = 0; j < dimension.size(); j++) {
                        dimensionDirections.add("positive");
                    }
                    scoringDirections.add(dimensionDirections);
                }
                log.info("[分维度量表调整] 使用默认得分方向（全部正向）");
            }

            // ========== 第二阶段：数据预处理（反向题转正向题） ==========

            // 获取所有题目的列号
            List<Integer> allColumns = new ArrayList<>();
            for (List<Integer> dimension : dimensions) {
                allColumns.addAll(dimension);
            }

            // 获取原始数据
            List<List<Double>> originalData = new ArrayList<>();
            for (Integer col : allColumns) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                originalData.add(new ArrayList<>(data));
            }

            // 记录哪些题目是反向计分，用于最后恢复
            Map<Integer, Boolean> reverseItemMap = new HashMap<>();

            // 将反向计分题转换为正向计分（统一处理）
            List<List<Double>> processedData = new ArrayList<>();
            for (int i = 0; i < originalData.size(); i++) {
                processedData.add(new ArrayList<>(originalData.get(i)));
            }

            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<String> dimDirections = scoringDirections.get(dimIdx);

                for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                    Integer col = dimension.get(itemIdx);
                    String direction = dimDirections.get(itemIdx);

                    if ("negative".equals(direction)) {
                        reverseItemMap.put(col, true);
                        int dataIdx = getDataIndex(col, allColumns);

                        // 根据题目的实际量表级数进行反向转换
                        List<Double> originalCol = processedData.get(dataIdx);
                        List<Double> reversedCol = new ArrayList<>();

                        // 获取该题目的量表级数
                        Integer cachedScaleLevel = questionScaleLevelsCache.get(col);
                        int actualScaleLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                        for (Double value : originalCol) {
                            if (value != null) {
                                // 动态计算反向转换公式：reversedValue = (actualScaleLevel + 1) - value
                                // 例如：5级量表(1-5)：6-1=5, 6-2=4, 6-3=3, 6-4=2, 6-5=1
                                // 例如：7级量表(1-7)：8-1=7, 8-2=6, 8-3=5, 8-4=4, 8-5=3, 8-6=2, 8-7=1
                                double reversedValue = (actualScaleLevel + 1) - value;
                                reversedCol.add(reversedValue);
                            } else {
                                reversedCol.add(null);
                            }
                        }
                        processedData.set(dataIdx, reversedCol);
                        log.info("[分维度量表调整] 题目{}从反向计分转换为正向计分进行统一处理（{}级量表）", col, actualScaleLevel);
                    }
                }
            }

            // 计算调整前的各维度信度（基于转换后的数据）
            List<Double> originalDimensionAlphas = new ArrayList<>();
            for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
                List<Integer> dimension = dimensions.get(dimIdx);
                List<List<Double>> dimensionData = new ArrayList<>();
                for (Integer col : dimension) {
                    int dataIdx = getDataIndex(col, allColumns);
                    dimensionData.add(processedData.get(dataIdx));
                }
                double alpha = calculateCronbachAlpha(dimensionData);
                originalDimensionAlphas.add(alpha);
                log.info("[分维度量表调整] 维度{}调整前信度: {}", dimension, alpha);
            }

            // 在计算信度和KMO之前，确保数据具有足够的变异性
            log.info("[分维度量表调整] 检查并修复数据变异性问题");
            if (!isDataValid(processedData)) {
                log.warn("[分维度量表调整] 检测到数据变异性问题，进行修复");
                processedData = ensureDataVariability(processedData);
                log.info("[分维度量表调整] 数据变异性修复完成");
            }

            // 计算调整前的总量表信度（基于转换后的数据）
            double originalTotalAlpha = calculateCronbachAlpha(processedData);
            log.info("[分维度量表调整] 总量表调整前信度: {}", originalTotalAlpha);

            // 计算调整前的KMO值（基于转换后的数据）
            double originalKMO = calculateKMOFromAdjustedData(processedData);
            log.info("[分维度量表调整] 调整前KMO值: {}", originalKMO);

            // ========== 第三阶段：数据调整（所有题目都按正向处理） ==========

            // 执行多维度调整（使用转换后的数据，所有题目都按正向处理）
            List<List<Double>> adjustedData = performMultiDimensionalAdjustmentUnified(
                processedData, dimensions, targetDimensionAlphas, targetTotalAlpha,
                targetKMO, targetInterDimensionCorrelation, tolerance, targetItemMeans, scaleLevel);

            // ========== 第四阶段：数据后处理和摘要生成 ==========

            // 保存用于摘要计算的数据（调整后但未反向转换的数据）
            List<List<Double>> summaryData = new ArrayList<>();
            for (List<Double> col : adjustedData) {
                summaryData.add(new ArrayList<>(col));
            }

            // 将反向计分题转换回原来的计分方式（用于存储到数据库）
            List<List<Double>> storageData = new ArrayList<>();
            for (List<Double> col : adjustedData) {
                storageData.add(new ArrayList<>(col));
            }

            if (!reverseItemMap.isEmpty()) {
                for (Map.Entry<Integer, Boolean> entry : reverseItemMap.entrySet()) {
                    Integer col = entry.getKey();
                    int dataIdx = getDataIndex(col, allColumns);

                    // 获取该题目的量表级数
                    Integer cachedScaleLevel = questionScaleLevelsCache.get(col);
                    int actualScaleLevel = (cachedScaleLevel != null) ? cachedScaleLevel : scaleLevel;

                    // 将正向计分转换回反向计分
                    List<Double> positiveCol = storageData.get(dataIdx);
                    List<Double> reversedCol = new ArrayList<>();
                    for (Double value : positiveCol) {
                        if (value != null) {
                            // 动态计算反向转换公式：reversedValue = (actualScaleLevel + 1) - value
                            // 例如：5级量表(1-5)：6-5=1, 6-4=2, 6-3=3, 6-2=4, 6-1=5
                            // 例如：7级量表(1-7)：8-7=1, 8-6=2, 8-5=3, 8-4=4, 8-3=5, 8-2=6, 8-1=7
                            double reversedValue = (actualScaleLevel + 1) - value;
                            reversedCol.add(reversedValue);
                        } else {
                            reversedCol.add(null);
                        }
                    }
                    storageData.set(dataIdx, reversedCol);
                    log.info("[分维度量表调整] 题目{}从正向计分转换回反向计分用于存储（{}级量表）", col, actualScaleLevel);
                }
            }

            // 生成变更记录（基于原始数据和存储数据的对比）
            List<List<Object>> changedCells = generateChangedCells(originalData, storageData, allColumns);

            // 先使用预估的调整后指标构建metrics，实际值会在saveAdjustedDataToMessage中重新计算
            List<Double> adjustedDimensionAlphas = new ArrayList<>(targetDimensionAlphas);
            double adjustedTotalAlpha = targetTotalAlpha != null ? targetTotalAlpha : originalTotalAlpha;
            double adjustedKMO = targetKMO != null ? targetKMO : originalKMO;

            // 构建包含调整前后数据的指标对象
            Map<String, Object> achievedMetrics = new HashMap<>();
            achievedMetrics.put("originalDimensionAlphas", originalDimensionAlphas);
            achievedMetrics.put("adjustedDimensionAlphas", adjustedDimensionAlphas);
            achievedMetrics.put("targetDimensionAlphas", targetDimensionAlphas);
            achievedMetrics.put("originalTotalAlpha", originalTotalAlpha);
            achievedMetrics.put("adjustedTotalAlpha", adjustedTotalAlpha);
            achievedMetrics.put("targetTotalAlpha", targetTotalAlpha);
            achievedMetrics.put("originalKMO", originalKMO);
            achievedMetrics.put("adjustedKMO", adjustedKMO);
            achievedMetrics.put("targetKMO", targetKMO);
            achievedMetrics.put("dimensions", dimensions);
            achievedMetrics.put("allColumns", allColumns);
            achievedMetrics.put("cellsChanged", changedCells.size());

            // 生成调整说明
            String explanation = generateMultiDimensionalExplanation(
                dimensions, originalDimensionAlphas, adjustedDimensionAlphas, targetDimensionAlphas,
                originalTotalAlpha, adjustedTotalAlpha, targetTotalAlpha,
                originalKMO, adjustedKMO, targetKMO,
                changedCells.size(), achievedMetrics);

            // 构建包含文字描述数据的表格摘要（使用调整后但未反向转换的内存数据）
            Map<String, Object> summaryMetrics = buildSummaryMetrics(
                dimensions, originalDimensionAlphas, adjustedDimensionAlphas, targetDimensionAlphas,
                originalTotalAlpha, adjustedTotalAlpha, targetTotalAlpha,
                originalKMO, adjustedKMO, targetKMO, changedCells.size(), targetItemMeans, scoringDirections,
                summaryData, allColumns);

            // 将详细表格数据添加到metrics中，让baseTools处理
            summaryMetrics.put("isMultiDimensionalAdjustment", true);
            summaryMetrics.put("detailedTableData", summaryMetrics.get("summaryTableData"));
            summaryMetrics.put("detailedTableHeaders", summaryMetrics.get("summaryTableHeaders"));

            // 添加必要信息让saveAdjustedDataToMessage能够重新计算实际指标
            summaryMetrics.put("dimensions", dimensions);
            summaryMetrics.put("allColumns", allColumns);
            summaryMetrics.put("targetDimensionAlphas", targetDimensionAlphas);
            summaryMetrics.put("targetTotalAlpha", targetTotalAlpha);
            summaryMetrics.put("targetKMO", targetKMO);
            summaryMetrics.put("targetItemMeans", targetItemMeans);
            summaryMetrics.put("scoringDirections", scoringDirections);
            summaryMetrics.put("originalDimensionAlphas", originalDimensionAlphas);
            summaryMetrics.put("originalTotalAlpha", originalTotalAlpha);
            summaryMetrics.put("originalKMO", originalKMO);

            // 生成配置文本JSON
            String configText = generateConfigText(dimensions, scaleLevel, targetDimensionAlphas, targetTotalAlpha,
                targetKMO, targetInterDimensionCorrelation, tolerance, targetItemMeans, scoringDirections,
                columnToQuestionMap, questionDataMap);

            // 将配置文本添加到metrics中
            summaryMetrics.put("configText", configText);

            // 最终保护：在保存数据之前进行最后的变异性检查，确保KMO不会为NaN
            log.info("[最终保护] 保存数据前进行最后的变异性检查");
            boolean needsFinalFix = false;

            // 检查每列的变异性
            for (Integer col : allColumns) {
                List<Double> columnData = baseTools.getNumericColumnData(sessionId, col - 1);
                if (columnData != null && !columnData.isEmpty()) {
                    DescriptiveStatistics stats = new DescriptiveStatistics();
                    columnData.forEach(stats::addValue);

                    if (stats.getVariance() <= 1e-3 || stats.getStandardDeviation() <= 0.05) {
                        log.warn("[最终保护] 第{}列变异性不足，需要修复", col);
                        needsFinalFix = true;
                        break;
                    }
                }
            }

            if (needsFinalFix) {
                log.warn("[最终保护] 检测到变异性问题，进行最后修复");
                // 重新确保所有数据的变异性
                List<List<Double>> finalData = new ArrayList<>();
                for (Integer col : allColumns) {
                    List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                    finalData.add(data);
                }
                finalData = ensureDataVariability(finalData);

                // 重新生成变更记录
                List<List<Object>> finalChangedCells = generateChangedCells(originalData, finalData, allColumns);

                log.info("[最终保护] 修复完成，重新保存数据");
                return baseTools.saveAdjustedDataToMessage(sessionId, finalChangedCells, explanation, summaryMetrics);
            }

            // 保存调整后的数据到数据库，并返回分析结果给AI
            return baseTools.saveAdjustedDataToMessage(sessionId, changedCells, explanation, summaryMetrics);

        } catch (Exception e) {
            log.error("[分维度量表调整] 调整失败", e);
            return "分维度量表调整失败: " + e.getMessage();
        }
    }

    /**
     * 执行多维度调整的核心算法 - 循环迭代版本
     * 使用while循环持续调整，直到所有指标（维度信度、总信度、KMO）都达标
     */
    private List<List<Double>> performMultiDimensionalAdjustmentUnified(
            List<List<Double>> processedData, List<List<Integer>> dimensions,
            List<Double> targetDimensionAlphas, Double targetTotalAlpha,
            Double targetKMO, Double targetInterDimensionCorrelation, Double tolerance,
            List<List<Double>> targetItemMeans, Integer scaleLevel) {

        log.info("[循环调整] 开始执行多维度循环调整算法，确保所有指标达标");

        List<List<Double>> bestData = new ArrayList<>();
        for (List<Double> col : processedData) {
            bestData.add(new ArrayList<>(col));
        }

        // 第一步：精确调整题目均值（如果指定）
        if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
            log.info("[循环调整] 第一步：精确调整题目均值");
            bestData = adjustItemMeansPrecisely(bestData, dimensions, targetItemMeans);
        }

        // 主循环：持续调整直到所有指标达标
        int maxMainIterations = 80; // 增加最大主循环次数
        int mainIteration = 0;
        boolean allTargetsAchieved = false;

        List<Integer> allColumns = getAllColumns(dimensions);

        // 记录最佳结果
        List<List<Double>> bestOverallData = new ArrayList<>();
        for (List<Double> col : bestData) {
            bestOverallData.add(new ArrayList<>(col));
        }
        double bestOverallScore = calculateOverallScore(bestData, dimensions, allColumns,
                                                       targetDimensionAlphas, targetTotalAlpha, targetKMO);

        while (!allTargetsAchieved && mainIteration < maxMainIterations) {
            mainIteration++;
            log.info("[循环调整] 第{}次主循环开始", mainIteration);

            // 记录本轮调整前的状态
            List<List<Double>> iterationStartData = new ArrayList<>();
            for (List<Double> col : bestData) {
                iterationStartData.add(new ArrayList<>(col));
            }

            // 步骤1：优先调整题目均值（如果偏差过大）
            if (targetItemMeans != null && !targetItemMeans.isEmpty()) {
                if (mainIteration % 3 == 1) { // 每3轮调整一次均值
                    log.info("[循环调整] 第{}次主循环 - 重新调整题目均值", mainIteration);
                    bestData = adjustItemMeansWithHighPriority(bestData, dimensions, targetItemMeans, allColumns);
                }
            }

            // 步骤2：强化调整各维度信度
            log.info("[循环调整] 第{}次主循环 - 强化调整各维度信度", mainIteration);
            bestData = adjustDimensionAlphasWithEnhancedStrategy(bestData, dimensions, targetDimensionAlphas, tolerance, mainIteration);
            // 关键：每次调整后立即检查变异性，防止KMO为NaN
            bestData = ensureDataVariability(bestData);

            // 步骤3：调整总量表信度（如果需要）
            if (targetTotalAlpha != null) {
                log.info("[循环调整] 第{}次主循环 - 调整总量表信度，目标: {}", mainIteration, targetTotalAlpha);
                bestData = adjustTotalAlphaIntelligently(bestData, targetTotalAlpha, tolerance);
                // 关键：每次调整后立即检查变异性
                bestData = ensureDataVariability(bestData);
            }

            // 步骤4：调整KMO值（如果需要）
            if (targetKMO != null) {
                log.info("[循环调整] 第{}次主循环 - 调整KMO值，目标: {}", mainIteration, targetKMO);
                bestData = adjustKMOIntelligently(bestData, targetKMO, tolerance);
                // 关键：每次调整后立即检查变异性
                bestData = ensureDataVariability(bestData);
            }

            // 步骤5：调整维度间相关性（如果需要）
            if (targetInterDimensionCorrelation != null) {
                log.info("[循环调整] 第{}次主循环 - 调整维度间相关性，目标: {}", mainIteration, targetInterDimensionCorrelation);
                bestData = adjustInterDimensionCorrelationsIntelligently(bestData, dimensions, targetInterDimensionCorrelation);
                // 关键：每次调整后立即检查变异性
                bestData = ensureDataVariability(bestData);
            }

            // 确保数据变异性
            bestData = ensureDataVariability(bestData);

            // 计算当前综合得分
            double currentScore = calculateOverallScore(bestData, dimensions, allColumns,
                                                      targetDimensionAlphas, targetTotalAlpha, targetKMO);

            // 更新最佳结果
            if (currentScore < bestOverallScore) {
                bestOverallScore = currentScore;
                bestOverallData = new ArrayList<>();
                for (List<Double> col : bestData) {
                    bestOverallData.add(new ArrayList<>(col));
                }
                log.info("[循环调整] 第{}次主循环找到更好结果，综合得分: {:.4f}", mainIteration, currentScore);
            }

            // 检查所有目标是否达成
            allTargetsAchieved = checkAllTargetsAchieved(bestData, dimensions, allColumns,
                                                       targetDimensionAlphas, targetTotalAlpha,
                                                       targetKMO, tolerance);

            if (allTargetsAchieved) {
                log.info("[循环调整] 第{}次主循环后所有目标达成！", mainIteration);
                break;
            } else {
                // 记录当前状态
                logCurrentStatus(bestData, dimensions, allColumns, targetDimensionAlphas,
                               targetTotalAlpha, targetKMO, mainIteration);

                // 如果连续多次没有改进，尝试不同的策略
                if (mainIteration % 8 == 0) {
                    log.info("[循环调整] 第{}次主循环，尝试数据重新初始化", mainIteration);
                    bestData = reinitializeDataForBetterConvergence(bestData, dimensions,
                                                                  targetDimensionAlphas, targetTotalAlpha, targetKMO, scaleLevel);
                } else if (mainIteration % 5 == 0) {
                    log.info("[循环调整] 第{}次主循环，回退到最佳结果重新尝试", mainIteration);
                    bestData = new ArrayList<>();
                    for (List<Double> col : bestOverallData) {
                        bestData.add(new ArrayList<>(col));
                    }
                }
            }
        }

        if (!allTargetsAchieved) {
            log.warn("[循环调整] 达到最大迭代次数({})，未能完全达成所有目标", maxMainIterations);
            // 执行最终的保守调整
            bestData = performFinalConservativeAdjustment(bestData, dimensions, targetDimensionAlphas,
                                                        targetTotalAlpha, targetKMO, tolerance);
        }

        // 最终验证
        log.info("[循环调整] 执行最终验证");
        logFinalResults(bestData, dimensions, allColumns, targetDimensionAlphas, targetTotalAlpha, targetKMO);

        return bestData;
    }

    /**
     * 检查所有目标是否达成
     */
    private boolean checkAllTargetsAchieved(List<List<Double>> data, List<List<Integer>> dimensions,
                                          List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                          Double targetTotalAlpha, Double targetKMO, Double tolerance) {

        // 检查各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            if (Math.abs(actualAlpha - targetAlpha) > tolerance) {
                log.debug("[目标检查] 维度{}信度未达标: 实际{:.3f}, 目标{:.3f}, 差距{:.3f}",
                         dimIdx + 1, actualAlpha, targetAlpha, Math.abs(actualAlpha - targetAlpha));
                return false;
            }
        }

        // 检查总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            if (Math.abs(actualTotalAlpha - targetTotalAlpha) > tolerance) {
                log.debug("[目标检查] 总信度未达标: 实际{:.3f}, 目标{:.3f}, 差距{:.3f}",
                         actualTotalAlpha, targetTotalAlpha, Math.abs(actualTotalAlpha - targetTotalAlpha));
                return false;
            }
        }

        // 检查KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            if (Double.isNaN(actualKMO) || Math.abs(actualKMO - targetKMO) > tolerance) {
                log.debug("[目标检查] KMO未达标: 实际{:.3f}, 目标{:.3f}, 差距{:.3f}",
                         actualKMO, targetKMO, Double.isNaN(actualKMO) ? Double.NaN : Math.abs(actualKMO - targetKMO));
                return false;
            }
        }

        return true;
    }

    /**
     * 记录当前状态
     */
    private void logCurrentStatus(List<List<Double>> data, List<List<Integer>> dimensions,
                                List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                Double targetTotalAlpha, Double targetKMO, int iteration) {

        log.info("[状态记录] 第{}次主循环后的状态:", iteration);

        // 记录各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            String status = Math.abs(actualAlpha - targetAlpha) <= 0.02 ? "✓达标" : "需优化";
            log.info("[状态记录] 维度{}信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    dimIdx + 1, actualAlpha, targetAlpha, Math.abs(actualAlpha - targetAlpha), status);
        }

        // 记录总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            String status = Math.abs(actualTotalAlpha - targetTotalAlpha) <= 0.02 ? "✓达标" : "需优化";
            log.info("[状态记录] 总信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualTotalAlpha, targetTotalAlpha, Math.abs(actualTotalAlpha - targetTotalAlpha), status);
        }

        // 记录KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            String status = (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= 0.02) ? "✓达标" : "需优化";
            log.info("[状态记录] KMO: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualKMO, targetKMO, Double.isNaN(actualKMO) ? Double.NaN : Math.abs(actualKMO - targetKMO), status);
        }
    }

    /**
     * 重新初始化数据以获得更好的收敛性
     */
    private List<List<Double>> reinitializeDataForBetterConvergence(List<List<Double>> data,
                                                                  List<List<Integer>> dimensions,
                                                                  List<Double> targetDimensionAlphas,
                                                                  Double targetTotalAlpha,
                                                                  Double targetKMO,
                                                                  Integer scaleLevel) {
        log.info("[数据重初始化] 开始重新初始化数据以改善收敛性");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 策略1：增加数据变异性
        for (int i = 0; i < result.size(); i++) {
            List<Double> col = result.get(i);
            double mean = col.stream().mapToDouble(Double::doubleValue).average().orElse(3.0);
            double std = calculateStandardDeviation(col);

            // 如果标准差太小，增加变异性
            if (std < 0.5) {
                for (int j = 0; j < col.size(); j++) {
                    double noise = (Math.random() - 0.5) * 0.3; // 小幅随机调整
                    double newValue = col.get(j) + noise;
                    // 根据传入的量表级数限制范围
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(j, newValue);
                }
                log.debug("[数据重初始化] 第{}列增加变异性，原标准差: {:.3f}，量表级数: {}", i + 1, std, currentScaleLevel);
            }
        }

        // 策略2：调整维度间相关性
        List<Integer> allColumns = getAllColumns(dimensions);
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);

            // 增强维度内相关性
            for (int i = 0; i < dimension.size() - 1; i++) {
                for (int j = i + 1; j < dimension.size(); j++) {
                    int col1Idx = getDataIndex(dimension.get(i), allColumns);
                    int col2Idx = getDataIndex(dimension.get(j), allColumns);

                    enhanceCorrelationBetweenItems(result.get(col1Idx), result.get(col2Idx), 0.1);
                }
            }
        }

        // 确保数据变异性
        result = ensureDataVariability(result);

        log.info("[数据重初始化] 数据重新初始化完成");
        return result;
    }

    /**
     * 执行最终的保守调整
     */
    private List<List<Double>> performFinalConservativeAdjustment(List<List<Double>> data,
                                                                List<List<Integer>> dimensions,
                                                                List<Double> targetDimensionAlphas,
                                                                Double targetTotalAlpha,
                                                                Double targetKMO,
                                                                Double tolerance) {
        log.info("[最终保守调整] 开始执行最终保守调整");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        // 保守调整各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(result.get(dataIdx));
            }

            double currentAlpha = calculateCronbachAlpha(dimensionData);
            if (Math.abs(currentAlpha - targetAlpha) > tolerance) {
                log.info("[最终保守调整] 保守调整维度{}信度: {:.3f} -> {:.3f}",
                        dimIdx + 1, currentAlpha, targetAlpha);

                // 使用保守策略调整
                List<List<Double>> adjustedDimensionData = adjustSingleDimensionConservatively(
                    dimensionData, targetAlpha, tolerance * 2); // 放宽容差

                // 将调整后的数据放回原位置
                for (int i = 0; i < dimension.size(); i++) {
                    Integer col = dimension.get(i);
                    int dataIdx = getDataIndex(col, allColumns);
                    result.set(dataIdx, adjustedDimensionData.get(i));
                }
            }
        }

        // 保守调整总信度
        if (targetTotalAlpha != null) {
            double currentTotalAlpha = calculateCronbachAlpha(result);
            if (Math.abs(currentTotalAlpha - targetTotalAlpha) > tolerance) {
                log.info("[最终保守调整] 保守调整总信度: {:.3f} -> {:.3f}",
                        currentTotalAlpha, targetTotalAlpha);
                result = adjustSingleDimensionConservatively(result, targetTotalAlpha, tolerance * 2);
            }
        }

        // 确保数据变异性
        result = ensureDataVariability(result);

        log.info("[最终保守调整] 最终保守调整完成");
        return result;
    }

    /**
     * 记录最终结果
     */
    private void logFinalResults(List<List<Double>> data, List<List<Integer>> dimensions,
                               List<Integer> allColumns, List<Double> targetDimensionAlphas,
                               Double targetTotalAlpha, Double targetKMO) {

        log.info("[最终结果] ========== 最终调整结果 ==========");

        // 记录各维度信度
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            String status = Math.abs(actualAlpha - targetAlpha) <= 0.02 ? "✓达标" : "未达标";
            log.info("[最终结果] 维度{}信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    dimIdx + 1, actualAlpha, targetAlpha, Math.abs(actualAlpha - targetAlpha), status);
        }

        // 记录总信度
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            String status = Math.abs(actualTotalAlpha - targetTotalAlpha) <= 0.02 ? "✓达标" : "未达标";
            log.info("[最终结果] 总信度: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualTotalAlpha, targetTotalAlpha, Math.abs(actualTotalAlpha - targetTotalAlpha), status);
        }

        // 记录KMO
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            String status = (!Double.isNaN(actualKMO) && Math.abs(actualKMO - targetKMO) <= 0.02) ? "✓达标" : "未达标";
            log.info("[最终结果] KMO: 实际{:.3f}, 目标{:.3f}, 差距{:.3f} - {}",
                    actualKMO, targetKMO, Double.isNaN(actualKMO) ? Double.NaN : Math.abs(actualKMO - targetKMO), status);
        }

        log.info("[最终结果] ========================================");
    }

    /**
     * 计算标准差
     */
    private double calculateStandardDeviation(List<Double> data) {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }

        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = data.stream()
                             .mapToDouble(x -> Math.pow(x - mean, 2))
                             .average()
                             .orElse(0.0);
        return Math.sqrt(variance);
    }

    /**
     * 计算综合得分（用于评估整体调整效果）
     * 得分越低表示越接近目标
     */
    private double calculateOverallScore(List<List<Double>> data, List<List<Integer>> dimensions,
                                       List<Integer> allColumns, List<Double> targetDimensionAlphas,
                                       Double targetTotalAlpha, Double targetKMO) {
        double totalScore = 0.0;

        // 维度信度得分（权重较高）
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(data.get(dataIdx));
            }

            double actualAlpha = calculateCronbachAlpha(dimensionData);
            double alphaGap = Math.abs(actualAlpha - targetAlpha);
            totalScore += alphaGap * 3.0; // 维度信度权重为3
        }

        // 总信度得分
        if (targetTotalAlpha != null) {
            double actualTotalAlpha = calculateCronbachAlpha(data);
            double totalAlphaGap = Math.abs(actualTotalAlpha - targetTotalAlpha);
            totalScore += totalAlphaGap * 2.0; // 总信度权重为2
        }

        // KMO得分
        if (targetKMO != null) {
            double actualKMO = calculateKMOFromAdjustedData(data);
            if (!Double.isNaN(actualKMO)) {
                double kmoGap = Math.abs(actualKMO - targetKMO);
                totalScore += kmoGap * 1.5; // KMO权重为1.5
            } else {
                totalScore += 1.0; // NaN的惩罚
            }
        }

        return totalScore;
    }

    /**
     * 高优先级调整题目均值
     */
    private List<List<Double>> adjustItemMeansWithHighPriority(List<List<Double>> data,
                                                             List<List<Integer>> dimensions,
                                                             List<List<Double>> targetItemMeans,
                                                             List<Integer> allColumns) {
        log.info("[高优先级均值调整] 开始强化调整题目均值");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 对每个维度的每个题目进行精确均值调整
        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMeanObj = dimTargetMeans.get(itemIdx);

                // 如果目标均值为null，跳过该题目的均值调整
                if (targetMeanObj == null) {
                    continue;
                }

                double targetMean = targetMeanObj;
                int dataIdx = getDataIndex(col, allColumns);

                List<Double> itemData = result.get(dataIdx);
                double currentMean = itemData.stream().mapToDouble(Double::doubleValue).average().orElse(3.0);
                double meanGap = targetMean - currentMean;

                // 如果均值偏差超过0.3，进行强化调整
                if (Math.abs(meanGap) > 0.3) {
                    log.info("[高优先级均值调整] 题目{}均值偏差过大: 当前{:.3f}, 目标{:.3f}, 差距{:.3f}",
                            col, currentMean, targetMean, meanGap);

                    // 获取该题目的量表级数
                    Integer scaleLevel = getQuestionScaleLevel(col, itemData);

                    // 使用更激进的调整策略
                    for (int i = 0; i < itemData.size(); i++) {
                        double currentValue = itemData.get(i);
                        double adjustment = meanGap * 0.8; // 80%的调整幅度
                        double newValue = currentValue + adjustment;

                        // 根据实际量表级数确保在合理范围内
                        newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                        itemData.set(i, newValue);
                    }

                    // 验证调整后的均值
                    double newMean = itemData.stream().mapToDouble(Double::doubleValue).average().orElse(3.0);
                    log.info("[高优先级均值调整] 题目{}调整后均值: {:.3f}（{}级量表）", col, newMean, scaleLevel);
                }
            }
        }

        log.info("[高优先级均值调整] 强化均值调整完成");
        return result;
    }

    /**
     * 增强策略调整各维度信度
     */
    private List<List<Double>> adjustDimensionAlphasWithEnhancedStrategy(List<List<Double>> data,
                                                                       List<List<Integer>> dimensions,
                                                                       List<Double> targetDimensionAlphas,
                                                                       Double tolerance,
                                                                       int iteration) {
        log.info("[增强信度调整] 开始增强策略调整各维度信度，第{}次迭代", iteration);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            double targetAlpha = targetDimensionAlphas.get(dimIdx);

            log.info("[增强信度调整] 开始调整维度{}，目标信度: {}", dimIdx + 1, targetAlpha);

            // 提取当前维度的数据
            List<List<Double>> dimensionData = new ArrayList<>();
            for (Integer col : dimension) {
                int dataIdx = getDataIndex(col, allColumns);
                dimensionData.add(result.get(dataIdx));
            }

            // 计算当前信度
            double currentAlpha = calculateCronbachAlpha(dimensionData);
            double alphaGap = targetAlpha - currentAlpha;

            log.info("[增强信度调整] 维度{}当前信度: {:.3f}, 目标信度: {:.3f}, 差距: {:.3f}",
                    dimIdx + 1, currentAlpha, targetAlpha, alphaGap);

            if (Math.abs(alphaGap) <= tolerance) {
                log.info("[增强信度调整] 维度{}信度已达标，跳过调整", dimIdx + 1);
                continue;
            }

            // 根据迭代次数选择不同强度的调整策略
            List<List<Double>> adjustedDimensionData;
            if (iteration <= 20) {
                // 前20次使用标准策略
                adjustedDimensionData = adjustSingleDimensionIntelligently(
                    dimensionData, targetAlpha, tolerance, "维度" + (dimIdx + 1));
            } else if (iteration <= 50) {
                // 21-50次使用增强策略
                adjustedDimensionData = adjustSingleDimensionWithEnhancedStrategy(
                    dimensionData, targetAlpha, tolerance, alphaGap, "维度" + (dimIdx + 1));
            } else {
                // 50次以后使用激进策略
                adjustedDimensionData = adjustSingleDimensionAggressively(
                    dimensionData, targetAlpha, tolerance, "维度" + (dimIdx + 1));
            }

            // 将调整后的数据放回原位置
            for (int i = 0; i < dimension.size(); i++) {
                Integer col = dimension.get(i);
                int dataIdx = getDataIndex(col, allColumns);
                result.set(dataIdx, adjustedDimensionData.get(i));
            }

            // 验证调整结果
            double finalAlpha = calculateCronbachAlpha(adjustedDimensionData);
            log.info("[增强信度调整] 维度{}调整完成，最终信度: {:.3f}", dimIdx + 1, finalAlpha);
        }

        return result;
    }

    /**
     * 增强策略调整单个维度信度
     */
    private List<List<Double>> adjustSingleDimensionWithEnhancedStrategy(List<List<Double>> data,
                                                                        double targetAlpha,
                                                                        Double tolerance,
                                                                        double alphaGap,
                                                                        String dimensionName) {
        log.info("[增强策略] {}开始增强策略调整，信度差距: {:.3f}", dimensionName, alphaGap);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 确保数据变异性
        result = ensureDataVariability(result);

        double currentAlpha = calculateCronbachAlpha(result);

        // 增强策略：更激进的调整
        int maxIterations = 40;
        for (int iteration = 0; iteration < maxIterations; iteration++) {

            if (alphaGap > 0) {
                // 需要提高信度：增强相关性
                result = enhanceCorrelationsAggressively(result, 0.15 + iteration * 0.01);
            } else {
                // 需要降低信度：增加噪声
                result = addControlledNoiseToData(result, 0.1 + iteration * 0.005);
            }

            // 确保数据变异性
            result = ensureDataVariability(result);

            double newAlpha = calculateCronbachAlpha(result);

            if (Math.abs(newAlpha - targetAlpha) <= tolerance) {
                log.info("[增强策略] {}第{}次迭代达到目标，信度: {:.3f}", dimensionName, iteration, newAlpha);
                break;
            }

            // 如果改进不明显，尝试不同的策略
            if (iteration > 0 && Math.abs(newAlpha - currentAlpha) < 0.005) {
                result = adjustVariancesForAlpha(result, targetAlpha, alphaGap);
            }

            currentAlpha = newAlpha;
        }

        log.info("[增强策略] {}增强策略调整完成，最终信度: {:.3f}", dimensionName, currentAlpha);
        return result;
    }

    /**
     * 激进策略调整单个维度信度
     */
    private List<List<Double>> adjustSingleDimensionAggressively(List<List<Double>> data,
                                                               double targetAlpha,
                                                               Double tolerance,
                                                               String dimensionName) {
        log.info("[激进策略] {}开始激进策略调整", dimensionName);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        // 激进策略：大幅度调整
        if (alphaGap > 0) {
            // 需要大幅提高信度
            log.info("[激进策略] {}需要大幅提高信度，差距: {:.3f}", dimensionName, alphaGap);

            // 策略1：强化所有题目间的相关性
            for (int i = 0; i < result.size() - 1; i++) {
                for (int j = i + 1; j < result.size(); j++) {
                    enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.25);
                }
            }

            // 策略2：调整方差使其更相似
            double targetVariance = calculateAverageVariance(result);
            for (List<Double> col : result) {
                adjustVarianceToTarget(col, targetVariance);
            }

        } else {
            // 需要降低信度
            log.info("[激进策略] {}需要降低信度，差距: {:.3f}", dimensionName, Math.abs(alphaGap));

            // 策略：增加大量随机噪声
            for (List<Double> col : result) {
                for (int i = 0; i < col.size(); i++) {
                    double noise = (Math.random() - 0.5) * 0.8; // 更大的噪声
                    double newValue = col.get(i) + noise;
                    // 使用当前设置的量表级数并限制范围
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(i, newValue);
                }
            }
        }

        // 确保数据变异性
        result = ensureDataVariability(result);

        double finalAlpha = calculateCronbachAlpha(result);
        log.info("[激进策略] {}激进策略调整完成，{:.3f} -> {:.3f}", dimensionName, currentAlpha, finalAlpha);

        return result;
    }

    /**
     * 激进地增强相关性
     */
    private List<List<Double>> enhanceCorrelationsAggressively(List<List<Double>> data, double enhancementFactor) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 计算所有题目的平均值
        List<Double> averageScores = new ArrayList<>();
        for (int i = 0; i < result.get(0).size(); i++) {
            double sum = 0.0;
            for (List<Double> col : result) {
                sum += col.get(i);
            }
            averageScores.add(sum / result.size());
        }

        // 让每个题目的得分向平均得分靠拢
        for (int colIdx = 0; colIdx < result.size(); colIdx++) {
            List<Double> col = result.get(colIdx);
            for (int i = 0; i < col.size(); i++) {
                double currentValue = col.get(i);
                double averageValue = averageScores.get(i);
                double adjustment = (averageValue - currentValue) * enhancementFactor;
                double newValue = currentValue + adjustment;
                // 使用当前设置的量表级数并限制范围
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 添加受控噪声到数据
     */
    private List<List<Double>> addControlledNoiseToData(List<List<Double>> data, double noiseLevel) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            List<Double> newCol = new ArrayList<>();
            for (Double value : col) {
                double noise = (Math.random() - 0.5) * 2 * noiseLevel;
                double newValue = value + noise;
                // 使用当前设置的量表级数并限制范围
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                newCol.add(newValue);
            }
            result.add(newCol);
        }
        return result;
    }

    /**
     * 调整方差以达到目标信度
     */
    private List<List<Double>> adjustVariancesForAlpha(List<List<Double>> data, double targetAlpha, double alphaGap) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 如果需要提高信度，减少方差差异
        if (alphaGap > 0) {
            double targetVariance = calculateAverageVariance(result);
            for (List<Double> col : result) {
                adjustVarianceToTarget(col, targetVariance);
            }
        }

        return result;
    }

    /**
     * 调整方差到目标值
     */
    private void adjustVarianceToTarget(List<Double> data, double targetVariance) {
        double currentVariance = calculateVariance(data);
        if (currentVariance <= 0.001) return; // 避免除零

        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(3.0);
        double scaleFactor = Math.sqrt(targetVariance / currentVariance);

        for (int i = 0; i < data.size(); i++) {
            double deviation = data.get(i) - mean;
            double newValue = mean + deviation * scaleFactor;
            // 使用当前设置的量表级数并限制范围
            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            data.set(i, newValue);
        }
    }

    /**
     * 计算方差
     */
    private double calculateVariance(List<Double> data) {
        if (data == null || data.isEmpty()) return 0.0;

        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        return data.stream()
                  .mapToDouble(x -> Math.pow(x - mean, 2))
                  .average()
                  .orElse(0.0);
    }



    /**
     * 检查数据有效性 - 重点检查同值问题和KMO计算所需的条件
     */
    private boolean isDataValid(List<List<Double>> data) {
        if (data == null || data.isEmpty()) {
            log.warn("[数据有效性检查] 数据为空");
            return false;
        }

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = data.get(i);
            if (col == null || col.isEmpty()) {
                log.warn("[数据有效性检查] 第{}列数据为空", i + 1);
                return false;
            }

            // 检查是否有有效数据
            List<Double> validValues = new ArrayList<>();
            for (Double value : col) {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    validValues.add(value);
                }
            }

            if (validValues.isEmpty()) {
                log.warn("[数据有效性检查] 第{}列没有有效数据", i + 1);
                return false;
            }

            // 关键检查：是否所有值都相同（这是KMO为NaN的主要原因）
            if (isAllValuesSame(validValues)) {
                log.error("[数据有效性检查] 第{}列所有值都相同({})，无法进行统计分析", i + 1, validValues.get(0));
                return false;
            }

            // 检查方差是否过小（提高阈值，确保KMO计算稳定）
            DescriptiveStatistics stats = new DescriptiveStatistics();
            validValues.forEach(stats::addValue);
            double variance = stats.getVariance();
            double stdDev = stats.getStandardDeviation();

            if (variance < 1e-6 || stdDev < 1e-3) {
                log.error("[数据有效性检查] 第{}列方差过小(方差:{}, 标准差:{})，无法支持KMO计算", i + 1, variance, stdDev);
                return false;
            }

            // 检查数据分布是否过于集中（至少需要2个不同的值）
            Set<Double> uniqueValues = new HashSet<>(validValues);
            if (uniqueValues.size() < 2) {
                log.error("[数据有效性检查] 第{}列只有{}个不同的值，无法进行相关分析", i + 1, uniqueValues.size());
                return false;
            }
        }

        // 额外检查：确保没有完全相同的列（这也会导致KMO计算问题）
        if (hasIdenticalColumns(data)) {
            log.error("[数据有效性检查] 存在完全相同的列，无法进行因子分析");
            return false;
        }

        return true;
    }

    /**
     * 检查是否所有值都相同
     */
    private boolean isAllValuesSame(List<Double> values) {
        if (values.size() <= 1) {
            return true;
        }

        Double firstValue = values.get(0);
        for (int i = 1; i < values.size(); i++) {
            if (Math.abs(values.get(i) - firstValue) > 1e-10) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否有完全相同的列（这会导致KMO计算异常）
     */
    private boolean hasIdenticalColumns(List<List<Double>> data) {
        if (data == null || data.size() < 2) {
            return false;
        }

        for (int i = 0; i < data.size(); i++) {
            for (int j = i + 1; j < data.size(); j++) {
                if (areColumnsIdentical(data.get(i), data.get(j))) {
                    log.warn("[同值检查] 第{}列和第{}列完全相同", i + 1, j + 1);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查两列是否完全相同
     */
    private boolean areColumnsIdentical(List<Double> col1, List<Double> col2) {
        if (col1.size() != col2.size()) {
            return false;
        }

        for (int i = 0; i < col1.size(); i++) {
            Double val1 = col1.get(i);
            Double val2 = col2.get(i);

            // 处理null值
            if (val1 == null && val2 == null) {
                continue;
            }
            if (val1 == null || val2 == null) {
                return false;
            }

            // 检查数值差异（考虑浮点精度）
            if (Math.abs(val1 - val2) > 1e-10) {
                return false;
            }
        }

        return true;
    }

    /**
     * 为数据添加最小变异性，防止同值问题 - 增强版本
     * 这是防止KMO为NaN的关键方法，确保数据满足统计分析的基本要求
     */
    private List<List<Double>> ensureDataVariability(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        boolean hasVariabilityIssues = false;

        log.info("[变异性保证] 开始检查和修复数据变异性问题");

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = data.get(i);
            List<Double> adjustedCol = new ArrayList<>();

            // 检查是否所有值都相同
            if (isAllValuesSame(col)) {
                log.warn("[变异性保证] 第{}列所有值都相同，添加变异性", i + 1);
                adjustedCol = addMinimalVariability(col);
                hasVariabilityIssues = true;
            } else {
                // 检查方差是否过小（提高阈值以确保KMO计算稳定）
                DescriptiveStatistics stats = new DescriptiveStatistics();
                col.forEach(value -> {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                });

                double variance = stats.getVariance();
                double stdDev = stats.getStandardDeviation();

                // 更严格的变异性检查 - 防止KMO为NaN的关键
                if (variance < 1e-3 || stdDev < 0.05) {
                    log.warn("[变异性保证] 第{}列方差过小(方差:{:.6f}, 标准差:{:.6f})，添加变异性", i + 1, variance, stdDev);
                    adjustedCol = addMinimalVariability(col);
                    hasVariabilityIssues = true;
                } else {
                    // 检查唯一值的数量 - 更严格的要求
                    Set<Double> uniqueValues = new HashSet<>();
                    for (Double value : col) {
                        if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                            uniqueValues.add(Math.round(value * 100.0) / 100.0); // 保留2位小数进行比较，更严格
                        }
                    }

                    // 要求至少有5个不同的值，确保足够的变异性
                    if (uniqueValues.size() < 5) {
                        log.warn("[变异性保证] 第{}列只有{}个不同的值，增加变异性", i + 1, uniqueValues.size());
                        adjustedCol = addMinimalVariability(col);
                        hasVariabilityIssues = true;
                    } else {
                        adjustedCol = new ArrayList<>(col);
                    }
                }
            }

            result.add(adjustedCol);
        }

        // 检查是否有完全相同的列
        if (hasIdenticalColumns(result)) {
            log.warn("[变异性保证] 检测到完全相同的列，进行差异化处理");
            result = differentiateIdenticalColumns(result);
            hasVariabilityIssues = true;
        }

        if (hasVariabilityIssues) {
            log.info("[变异性保证] 变异性修复完成，重新验证数据质量");
            // 最终验证
            if (!isDataValid(result)) {
                log.error("[变异性保证] 修复后数据仍然无效，可能需要更激进的修复策略");
            }
        } else {
            log.info("[变异性保证] 数据变异性检查通过，无需修复");
        }

        return result;
    }

    /**
     * 差异化完全相同的列
     */
    private List<List<Double>> differentiateIdenticalColumns(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            List<Double> col = new ArrayList<>(data.get(i));

            // 为每一列添加微小的、独特的变异
            for (int j = 0; j < col.size(); j++) {
                if (col.get(j) != null && !Double.isNaN(col.get(j)) && Double.isFinite(col.get(j))) {
                    // 基于列索引和行索引添加独特的微小变异
                    double uniqueVariation = (i * 0.001 + j * 0.0001) % 0.01;
                    double newValue = col.get(j) + uniqueVariation;
                    // 根据当前设置的量表级数并限制范围
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(j, newValue);
                }
            }

            result.add(col);
        }

        return result;
    }

    /**
     * 为同值数据添加最小变异性 - 增强版本
     * 确保产生足够的变异性以支持KMO计算
     */
    private List<Double> addMinimalVariability(List<Double> col) {
        List<Double> result = new ArrayList<>();

        if (col.isEmpty()) {
            return result;
        }

        // 获取基准值
        Double baseValue = null;
        for (Double value : col) {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                baseValue = value;
                break;
            }
        }

        if (baseValue == null) {
            return new ArrayList<>(col);
        }

        log.info("[变异性增强] 为同值数据添加变异性，基准值: {}", baseValue);

        // 创建更有意义的变异性分布
        List<Double> variationPattern = createVariationPattern(col.size(), baseValue);

        for (int i = 0; i < col.size(); i++) {
            Double originalValue = col.get(i);

            if (originalValue != null && !Double.isNaN(originalValue) && Double.isFinite(originalValue)) {
                // 使用预定义的变异模式，确保有足够的分散性
                double newValue = variationPattern.get(i);

                // 根据当前设置的量表级数并确保在合理范围内
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                result.add(newValue);
            } else {
                result.add(originalValue);
            }
        }

        // 验证变异性是否足够
        DescriptiveStatistics stats = new DescriptiveStatistics();
        result.forEach(value -> {
            if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                stats.addValue(value);
            }
        });

        log.info("[变异性增强] 调整后方差: {:.6f}, 标准差: {:.6f}", stats.getVariance(), stats.getStandardDeviation());

        return result;
    }

    /**
     * 创建有意义的变异模式，确保数据有足够的分散性 - 防止KMO为NaN的关键
     */
    private List<Double> createVariationPattern(int size, double baseValue) {
        List<Double> pattern = new ArrayList<>();
        Random random = new Random();

        // 使用当前设置的量表级数
        int scaleLevel = currentScaleLevel != null ? currentScaleLevel : 5;

        // 创建至少5个不同的目标值，确保足够的变异性
        double[] targetValues = new double[5];
        double range = scaleLevel - 1.0; // 量表范围

        // 在量表范围内均匀分布5个目标值
        for (int i = 0; i < 5; i++) {
            targetValues[i] = 1.0 + (range * i / 4.0);
        }

        // 如果基准值接近边界，调整分布策略
        double lowerBoundary = 1.5;
        double upperBoundary = scaleLevel - 0.5;

        if (baseValue <= lowerBoundary) {
            // 基准值较低，向上分布
            for (int i = 0; i < 5; i++) {
                targetValues[i] = 1.0 + (i * 0.5);
                targetValues[i] = Math.min(targetValues[i], (double)scaleLevel);
            }
        } else if (baseValue >= upperBoundary) {
            // 基准值较高，向下分布
            for (int i = 0; i < 5; i++) {
                targetValues[i] = scaleLevel - (4 - i) * 0.5;
                targetValues[i] = Math.max(targetValues[i], 1.0);
            }
        }

        // 按比例分配不同的值，确保每个目标值都有数据点
        for (int i = 0; i < size; i++) {
            int targetIndex = i % 5; // 循环使用5个目标值
            double targetValue = targetValues[targetIndex];

            // 添加小幅随机变异，但保持在目标值附近
            double noise = random.nextGaussian() * 0.15;
            targetValue += noise;

            // 确保在有效范围内
            targetValue = Math.max(1.0, Math.min((double)scaleLevel, targetValue));
            pattern.add(targetValue);
        }

        // 打乱顺序，避免规律性
        java.util.Collections.shuffle(pattern);

        // 验证创建的模式确实有足够的变异性
        Set<Double> uniqueValues = new HashSet<>();
        for (Double value : pattern) {
            uniqueValues.add(Math.round(value * 100.0) / 100.0);
        }

        log.info("[变异性创建] 创建了{}个数据点，包含{}个不同的值", size, uniqueValues.size());

        return pattern;
    }

    /**
     * 精确调整题目均值 - 智能算法
     * 通过迭代优化确保均值精确达到目标值
     */
    private List<List<Double>> adjustItemMeansPrecisely(List<List<Double>> data,
                                                       List<List<Integer>> dimensions,
                                                       List<List<Double>> targetItemMeans) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        List<Integer> allColumns = getAllColumns(dimensions);

        for (int dimIdx = 0; dimIdx < dimensions.size(); dimIdx++) {
            List<Integer> dimension = dimensions.get(dimIdx);
            List<Double> dimTargetMeans = targetItemMeans.get(dimIdx);

            for (int itemIdx = 0; itemIdx < dimension.size(); itemIdx++) {
                Integer col = dimension.get(itemIdx);
                Double targetMean = dimTargetMeans.get(itemIdx);
                int dataIdx = getDataIndex(col, allColumns);

                log.info("[精确均值调整] 调整维度{}题目{}，目标均值: {}", dimIdx + 1, col, targetMean);

                // 如果目标均值为null，跳过调整
                if (targetMean == null) {
                    double actualMean = result.get(dataIdx).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                    log.info("[精确均值调整] 题目{}调整完成，实际均值: {:.3f}, 目标均值: {:.3f}",
                            col, actualMean, targetMean);
                    continue;
                }

                // 获取该题目的量表级数
                Integer scaleLevel = getQuestionScaleLevel(col, result.get(dataIdx));

                List<Double> adjustedCol = adjustSingleItemMeanPrecisely(result.get(dataIdx), targetMean, scaleLevel);
                result.set(dataIdx, adjustedCol);

                // 验证调整结果
                double actualMean = adjustedCol.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                log.info("[精确均值调整] 题目{}调整完成，实际均值: {:.3f}, 目标均值: {:.3f}（{}级量表）",
                        col, actualMean, targetMean, scaleLevel);
            }
        }

        return result;
    }

    /**
     * 精确调整单个题目的均值
     */
    private List<Double> adjustSingleItemMeanPrecisely(List<Double> originalData, Double targetMean, Integer scaleLevel) {
        if (targetMean == null) {
            return new ArrayList<>(originalData);
        }

        // 如果没有提供量表级数，从数据中检测
        if (scaleLevel == null) {
            scaleLevel = currentScaleLevel;
        }

        List<Double> result = new ArrayList<>(originalData);
        double currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        if (Math.abs(currentMean - targetMean) < 0.001) {
            return result; // 已经足够接近
        }

        log.info("[精确均值调整] 开始调整，当前均值: {:.3f}, 目标均值: {:.3f}, 量表级数: {}",
                currentMean, targetMean, scaleLevel);

        // 使用迭代优化方法
        int maxIterations = 50;
        for (int iteration = 0; iteration < maxIterations; iteration++) {
            currentMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double gap = targetMean - currentMean;

            if (Math.abs(gap) < 0.001) {
                break; // 达到目标精度
            }

            // 智能调整策略：根据gap大小选择调整幅度
            double adjustmentStrength = Math.min(0.5, Math.abs(gap));

            // 选择需要调整的数据点
            List<Integer> adjustIndices = selectAdjustmentIndices(result, gap > 0, scaleLevel);

            for (Integer idx : adjustIndices) {
                double currentValue = result.get(idx);
                double adjustment = gap > 0 ? adjustmentStrength : -adjustmentStrength;
                double newValue = currentValue + adjustment;

                // 根据实际量表级数确保在合理范围内
                newValue = Math.max(1.0, Math.min((double)scaleLevel, newValue));
                result.set(idx, newValue);
            }
        }

        double finalMean = result.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[精确均值调整] 调整完成，最终均值: {:.3f}, 目标均值: {:.3f}", finalMean, targetMean);

        return result;
    }

    /**
     * 选择需要调整的数据点索引
     */
    private List<Integer> selectAdjustmentIndices(List<Double> data, boolean increaseDirection, Integer scaleLevel) {
        List<Integer> indices = new ArrayList<>();

        // 根据量表级数动态计算阈值
        double midPoint = (1.0 + scaleLevel) / 2.0; // 量表的中点
        double upperThreshold = midPoint + (scaleLevel - 1) * 0.3; // 上阈值
        double lowerThreshold = midPoint - (scaleLevel - 1) * 0.3; // 下阈值

        // 根据调整方向选择合适的数据点
        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);

            if (increaseDirection) {
                // 需要增加均值，选择较小的值进行调整
                if (value < upperThreshold) {
                    indices.add(i);
                }
            } else {
                // 需要减少均值，选择较大的值进行调整
                if (value > lowerThreshold) {
                    indices.add(i);
                }
            }
        }

        // 如果没有合适的点，随机选择一些点
        if (indices.isEmpty()) {
            int adjustCount = Math.max(1, data.size() / 10);
            for (int i = 0; i < adjustCount; i++) {
                indices.add((int) (Math.random() * data.size()));
            }
        }

        log.debug("[数据点选择] 量表级数: {}, 中点: {:.2f}, 上阈值: {:.2f}, 下阈值: {:.2f}, 选择了{}个数据点",
                scaleLevel, midPoint, upperThreshold, lowerThreshold, indices.size());

        return indices;
    }



    /**
     * 智能调整单个维度的信度 - 核心算法
     */
    private List<List<Double>> adjustSingleDimensionIntelligently(List<List<Double>> data,
                                                                 double targetAlpha,
                                                                 double tolerance,
                                                                 String dimensionName) {
        // 确保数据变异性
        List<List<Double>> currentData = ensureDataVariability(data);

        double currentAlpha = calculateCronbachAlpha(currentData);
        double initialAlpha = currentAlpha;

        log.info("[智能单维度调整] {}开始调整，初始信度: {:.3f}, 目标信度: {:.3f}",
                dimensionName, initialAlpha, targetAlpha);

        if (Math.abs(currentAlpha - targetAlpha) <= tolerance) {
            log.info("[智能单维度调整] {}信度已达标", dimensionName);
            return currentData;
        }

        // 智能策略选择
        AdjustmentStrategy strategy = selectOptimalStrategy(currentAlpha, targetAlpha, currentData);
        log.info("[智能单维度调整] {}选择调整策略: {}", dimensionName, strategy.name());

        List<List<Double>> bestData = new ArrayList<>();
        for (List<Double> col : currentData) {
            bestData.add(new ArrayList<>(col));
        }
        double bestAlpha = currentAlpha;

        int maxIterations = 30;
        int consecutiveFailures = 0;

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            // 根据策略执行调整
            List<List<Double>> newData = executeAdjustmentStrategy(currentData, targetAlpha, strategy, iteration);

            // 确保数据变异性
            newData = ensureDataVariability(newData);

            // 验证数据有效性
            if (!isDataValid(newData)) {
                log.warn("[智能单维度调整] {}第{}次迭代产生无效数据，尝试修复", dimensionName, iteration);
                newData = ensureDataVariability(newData);
                if (!isDataValid(newData)) {
                    consecutiveFailures++;
                    if (consecutiveFailures >= 3) {
                        log.error("[智能单维度调整] {}连续失败，停止调整", dimensionName);
                        break;
                    }
                    continue;
                }
            }

            double newAlpha = calculateCronbachAlpha(newData);
            double improvement = Math.abs(newAlpha - targetAlpha) - Math.abs(currentAlpha - targetAlpha);

            if (improvement < 0) { // 有改进
                currentData = newData;
                currentAlpha = newAlpha;
                consecutiveFailures = 0;

                // 更新最佳结果
                if (Math.abs(newAlpha - targetAlpha) < Math.abs(bestAlpha - targetAlpha)) {
                    bestData = new ArrayList<>();
                    for (List<Double> col : newData) {
                        bestData.add(new ArrayList<>(col));
                    }
                    bestAlpha = newAlpha;
                }

                log.debug("[智能单维度调整] {}第{}次迭代成功，信度: {:.3f} -> {:.3f}",
                         dimensionName, iteration, currentAlpha, newAlpha);

                // 检查是否达到目标
                if (Math.abs(newAlpha - targetAlpha) <= tolerance) {
                    log.info("[智能单维度调整] {}第{}次迭代达到目标", dimensionName, iteration);
                    break;
                }
            } else {
                consecutiveFailures++;
                if (consecutiveFailures >= 5) {
                    // 尝试切换策略
                    strategy = switchStrategy(strategy, currentAlpha, targetAlpha);
                    log.info("[智能单维度调整] {}切换到新策略: {}", dimensionName, strategy.name());
                    consecutiveFailures = 0;
                }
            }
        }

        log.info("[智能单维度调整] {}调整完成，{:.3f} -> {:.3f}，目标: {:.3f}",
                dimensionName, initialAlpha, bestAlpha, targetAlpha);

        return bestData;
    }

    /**
     * 调整策略枚举
     */
    private enum AdjustmentStrategy {
        CORRELATION_ENHANCEMENT,  // 增强相关性策略
        VARIANCE_ADJUSTMENT,      // 方差调整策略
        MEAN_SHIFT,              // 均值偏移策略
        NOISE_INJECTION,         // 噪声注入策略
        HYBRID_APPROACH          // 混合策略
    }

    /**
     * 选择最优调整策略
     */
    private AdjustmentStrategy selectOptimalStrategy(double currentAlpha, double targetAlpha, List<List<Double>> data) {
        double alphaGap = targetAlpha - currentAlpha;

        // 计算数据特征
        double avgCorrelation = calculateAverageCorrelation(data);
        double avgVariance = calculateAverageVariance(data);

        log.info("[策略选择] 当前信度: {:.3f}, 目标信度: {:.3f}, 平均相关性: {:.3f}, 平均方差: {:.3f}",
                currentAlpha, targetAlpha, avgCorrelation, avgVariance);

        if (Math.abs(alphaGap) > 0.2) {
            // 大幅调整需要混合策略
            return AdjustmentStrategy.HYBRID_APPROACH;
        } else if (alphaGap > 0.1) {
            // 需要提高信度，增强相关性
            return AdjustmentStrategy.CORRELATION_ENHANCEMENT;
        } else if (alphaGap < -0.1) {
            // 需要降低信度，注入噪声
            return AdjustmentStrategy.NOISE_INJECTION;
        } else if (avgVariance < 0.5) {
            // 方差过小，调整方差
            return AdjustmentStrategy.VARIANCE_ADJUSTMENT;
        } else {
            // 微调，使用均值偏移
            return AdjustmentStrategy.MEAN_SHIFT;
        }
    }

    /**
     * 切换调整策略
     */
    private AdjustmentStrategy switchStrategy(AdjustmentStrategy currentStrategy, double currentAlpha, double targetAlpha) {
        switch (currentStrategy) {
            case CORRELATION_ENHANCEMENT:
                return AdjustmentStrategy.VARIANCE_ADJUSTMENT;
            case VARIANCE_ADJUSTMENT:
                return AdjustmentStrategy.MEAN_SHIFT;
            case MEAN_SHIFT:
                return AdjustmentStrategy.NOISE_INJECTION;
            case NOISE_INJECTION:
                return AdjustmentStrategy.HYBRID_APPROACH;
            case HYBRID_APPROACH:
            default:
                return AdjustmentStrategy.CORRELATION_ENHANCEMENT;
        }
    }

    /**
     * 执行调整策略
     */
    private List<List<Double>> executeAdjustmentStrategy(List<List<Double>> data, double targetAlpha,
                                                        AdjustmentStrategy strategy, int iteration) {
        switch (strategy) {
            case CORRELATION_ENHANCEMENT:
                return enhanceCorrelationsForReliability(data, targetAlpha);
            case VARIANCE_ADJUSTMENT:
                return adjustVariancesForReliability(data, targetAlpha);
            case MEAN_SHIFT:
                return shiftMeansForReliability(data, targetAlpha);
            case NOISE_INJECTION:
                return injectNoiseForReliability(data, targetAlpha);
            case HYBRID_APPROACH:
                return hybridAdjustmentApproach(data, targetAlpha, iteration);
            default:
                return new ArrayList<>(data);
        }
    }

    /**
     * 计算平均相关性
     */
    private double calculateAverageCorrelation(List<List<Double>> data) {
        if (data.size() < 2) return 0.0;

        double sumCorrelation = 0.0;
        int count = 0;

        for (int i = 0; i < data.size(); i++) {
            for (int j = i + 1; j < data.size(); j++) {
                double correlation = calculateCorrelation(data.get(i), data.get(j));
                if (!Double.isNaN(correlation) && Double.isFinite(correlation)) {
                    sumCorrelation += Math.abs(correlation);
                    count++;
                }
            }
        }

        return count > 0 ? sumCorrelation / count : 0.0;
    }

    /**
     * 计算平均方差
     */
    private double calculateAverageVariance(List<List<Double>> data) {
        double sumVariance = 0.0;
        int count = 0;

        for (List<Double> col : data) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            double variance = stats.getVariance();
            if (!Double.isNaN(variance) && Double.isFinite(variance)) {
                sumVariance += variance;
                count++;
            }
        }

        return count > 0 ? sumVariance / count : 0.0;
    }

    /**
     * 增强相关性策略 - 提高信度
     */
    private List<List<Double>> enhanceCorrelationsForReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        if (currentAlpha >= targetAlpha) {
            return result;
        }

        // 计算各项目间的相关性
        double[][] correlations = calculateItemCorrelations(result);

        // 找到相关性较低的项目对，增强它们的相关性
        for (int i = 0; i < result.size(); i++) {
            for (int j = i + 1; j < result.size(); j++) {
                if (correlations[i][j] < 0.3) { // 相关性较低
                    enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.1);
                }
            }
        }

        return result;
    }

    /**
     * 方差调整策略
     */
    private List<List<Double>> adjustVariancesForReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 调整各项目的方差，使其更接近理想值
        for (int i = 0; i < result.size(); i++) {
            List<Double> col = result.get(i);
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);

            double currentVariance = stats.getVariance();
            double targetVariance = 1.0; // 理想方差

            if (Math.abs(currentVariance - targetVariance) > 0.1) {
                adjustColumnVariance(col, targetVariance);
            }
        }

        return result;
    }

    /**
     * 均值偏移策略
     */
    private List<List<Double>> shiftMeansForReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        // 微调各项目的均值
        for (List<Double> col : result) {
            double currentMean = col.stream().mapToDouble(Double::doubleValue).average().orElse(3.0);
            double adjustment = alphaGap > 0 ? 0.05 : -0.05; // 小幅调整

            // 使用当前设置的量表级数
            for (int i = 0; i < col.size(); i++) {
                double newValue = col.get(i) + adjustment;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 噪声注入策略 - 降低信度
     */
    private List<List<Double>> injectNoiseForReliability(List<List<Double>> data, double targetAlpha) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        if (currentAlpha <= targetAlpha) {
            return result;
        }

        // 注入随机噪声来降低一致性
        Random random = new Random();
        double noiseLevel = (currentAlpha - targetAlpha) * 0.5; // 噪声水平

        for (List<Double> col : result) {
            // 使用当前设置的量表级数
            for (int i = 0; i < col.size(); i++) {
                if (random.nextDouble() < 0.3) { // 30%的概率添加噪声
                    double noise = (random.nextGaussian() - 0.5) * noiseLevel;
                    double newValue = col.get(i) + noise;
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 混合调整策略
     */
    private List<List<Double>> hybridAdjustmentApproach(List<List<Double>> data, double targetAlpha, int iteration) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(result);
        double alphaGap = targetAlpha - currentAlpha;

        // 根据迭代次数和差距选择不同的混合策略
        if (iteration % 3 == 0) {
            result = enhanceCorrelationsForReliability(result, targetAlpha);
        } else if (iteration % 3 == 1) {
            result = adjustVariancesForReliability(result, targetAlpha);
        } else {
            if (alphaGap > 0) {
                result = shiftMeansForReliability(result, targetAlpha);
            } else {
                result = injectNoiseForReliability(result, targetAlpha);
            }
        }

        return result;
    }

    /**
     * 增强两个项目间的相关性
     */
    private void enhanceCorrelationBetweenItems(List<Double> item1, List<Double> item2, double targetIncrease) {
        if (item1.size() != item2.size()) return;

        // 计算当前相关性
        double currentCorr = calculateCorrelation(item1, item2);

        // 通过调整数据点来增强相关性
        for (int i = 0; i < item1.size(); i++) {
            if (Math.random() < 0.3) { // 30%的概率调整
                double val1 = item1.get(i);
                double val2 = item2.get(i);

                // 让两个值更接近
                double avg = (val1 + val2) / 2.0;
                double adjustment = targetIncrease * 0.5;

                double newVal1 = val1 + (avg - val1) * adjustment;
                double newVal2 = val2 + (avg - val2) * adjustment;

                // 使用当前设置的量表级数并限制范围
                newVal1 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal1));
                newVal2 = Math.max(1.0, Math.min((double)currentScaleLevel, newVal2));

                item1.set(i, newVal1);
                item2.set(i, newVal2);
            }
        }
    }

    /**
     * 调整列的方差
     */
    private void adjustColumnVariance(List<Double> column, double targetVariance) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        column.forEach(stats::addValue);

        double currentMean = stats.getMean();
        double currentVariance = stats.getVariance();

        if (currentVariance <= 0) return;

        double scaleFactor = Math.sqrt(targetVariance / currentVariance);

        // 使用当前设置的量表级数
        for (int i = 0; i < column.size(); i++) {
            double value = column.get(i);
            double deviation = value - currentMean;
            double newValue = currentMean + deviation * scaleFactor;

            newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
            column.set(i, newValue);
        }
    }

    /**
     * 智能调整总量表信度
     */
    private List<List<Double>> adjustTotalAlphaIntelligently(List<List<Double>> data,
                                                            Double targetTotalAlpha,
                                                            Double tolerance) {
        log.info("[智能总信度调整] 开始调整总量表信度，目标: {}", targetTotalAlpha);

        double currentAlpha = calculateCronbachAlpha(data);
        log.info("[智能总信度调整] 当前总信度: {:.3f}, 目标: {:.3f}", currentAlpha, targetTotalAlpha);

        if (Math.abs(currentAlpha - targetTotalAlpha) <= tolerance) {
            log.info("[智能总信度调整] 总信度已达标");
            return data;
        }

        return adjustSingleDimensionIntelligently(data, targetTotalAlpha, tolerance, "总量表");
    }

    /**
     * 智能调整KMO值 - 核心方法
     */
    private List<List<Double>> adjustKMOIntelligently(List<List<Double>> data,
                                                     Double targetKMO,
                                                     Double tolerance) {
        log.info("[智能KMO调整] 开始调整KMO值，目标: {}", targetKMO);

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 确保数据变异性
        result = ensureDataVariability(result);

        double currentKMO = calculateKMOFromAdjustedData(result);
        log.info("[智能KMO调整] 当前KMO: {:.3f}, 目标: {:.3f}", currentKMO, targetKMO);

        if (Double.isNaN(currentKMO) || currentKMO == 0.0) {
            log.warn("[智能KMO调整] 当前KMO无效，进行数据修复");
            result = repairDataForKMO(result);
            currentKMO = calculateKMOFromAdjustedData(result);
            log.info("[智能KMO调整] 修复后KMO: {:.3f}", currentKMO);
        }

        if (Math.abs(currentKMO - targetKMO) <= tolerance) {
            log.info("[智能KMO调整] KMO已达标");
            return result;
        }

        // 使用迭代优化方法调整KMO
        int maxIterations = 20;
        List<List<Double>> bestData = new ArrayList<>();
        for (List<Double> col : result) {
            bestData.add(new ArrayList<>(col));
        }
        double bestKMO = currentKMO;

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            List<List<Double>> newData = adjustDataForKMOTarget(result, targetKMO, iteration);

            // 确保数据变异性
            newData = ensureDataVariability(newData);

            if (!isDataValid(newData)) {
                log.warn("[智能KMO调整] 第{}次迭代产生无效数据，跳过", iteration);
                continue;
            }

            double newKMO = calculateKMOFromAdjustedData(newData);

            if (Double.isNaN(newKMO) || newKMO == 0.0) {
                log.warn("[智能KMO调整] 第{}次迭代KMO无效，尝试修复", iteration);
                newData = repairDataForKMO(newData);
                newKMO = calculateKMOFromAdjustedData(newData);
            }

            if (!Double.isNaN(newKMO) && newKMO > 0.0) {
                double improvement = Math.abs(newKMO - targetKMO) - Math.abs(currentKMO - targetKMO);

                if (improvement < 0) { // 有改进
                    result = newData;
                    currentKMO = newKMO;

                    if (Math.abs(newKMO - targetKMO) < Math.abs(bestKMO - targetKMO)) {
                        bestData = new ArrayList<>();
                        for (List<Double> col : newData) {
                            bestData.add(new ArrayList<>(col));
                        }
                        bestKMO = newKMO;
                    }

                    log.debug("[智能KMO调整] 第{}次迭代成功，KMO: {:.3f} -> {:.3f}", iteration, currentKMO, newKMO);

                    if (Math.abs(newKMO - targetKMO) <= tolerance) {
                        log.info("[智能KMO调整] 第{}次迭代达到目标", iteration);
                        break;
                    }
                }
            }
        }

        log.info("[智能KMO调整] 调整完成，最终KMO: {:.3f}", bestKMO);
        return bestData;
    }

    /**
     * 修复数据以支持KMO计算
     */
    private List<List<Double>> repairDataForKMO(List<List<Double>> data) {
        log.info("[KMO数据修复] 开始修复数据以支持KMO计算");

        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 1. 确保每列都有足够的变异性
        for (int i = 0; i < result.size(); i++) {
            List<Double> col = result.get(i);

            // 检查是否所有值都相同
            if (isAllValuesSame(col)) {
                log.warn("[KMO数据修复] 第{}列所有值相同，添加变异性", i + 1);
                result.set(i, createVariedColumn(col));
            } else {
                // 检查方差是否过小
                DescriptiveStatistics stats = new DescriptiveStatistics();
                col.forEach(stats::addValue);
                if (stats.getVariance() < 0.01) {
                    log.warn("[KMO数据修复] 第{}列方差过小，增加变异性", i + 1);
                    result.set(i, enhanceColumnVariability(col));
                }
            }
        }

        // 2. 确保列之间不完全相同
        result = eliminateIdenticalColumns(result);

        // 3. 确保相关矩阵可逆
        result = ensureMatrixInvertibility(result);

        log.info("[KMO数据修复] 数据修复完成");
        return result;
    }

    /**
     * 创建有变异性的列
     */
    private List<Double> createVariedColumn(List<Double> originalCol) {
        List<Double> result = new ArrayList<>();

        if (originalCol.isEmpty()) {
            return result;
        }

        Double baseValue = originalCol.get(0);
        if (baseValue == null) baseValue = 3.0;

        // 使用当前设置的量表级数
        // 创建正态分布的数据
        Random random = new Random();
        for (int i = 0; i < originalCol.size(); i++) {
            double value = baseValue + random.nextGaussian() * 0.5;
            value = Math.max(1.0, Math.min((double)currentScaleLevel, value));
            result.add(value);
        }

        return result;
    }

    /**
     * 增强列的变异性
     */
    private List<Double> enhanceColumnVariability(List<Double> originalCol) {
        List<Double> result = new ArrayList<>(originalCol);
        Random random = new Random();

        // 使用当前设置的量表级数
        // 对部分数据点添加随机变异
        for (int i = 0; i < result.size(); i++) {
            if (random.nextDouble() < 0.4) { // 40%的概率添加变异
                double variation = random.nextGaussian() * 0.3;
                double newValue = result.get(i) + variation;
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                result.set(i, newValue);
            }
        }

        return result;
    }

    /**
     * 消除完全相同的列
     */
    private List<List<Double>> eliminateIdenticalColumns(List<List<Double>> data) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 检查并修复相同的列
        for (int i = 0; i < result.size(); i++) {
            for (int j = i + 1; j < result.size(); j++) {
                if (areColumnsIdentical(result.get(i), result.get(j))) {
                    log.warn("[KMO数据修复] 第{}列和第{}列完全相同，进行差异化", i + 1, j + 1);

                    // 对第j列添加微小的差异
                    List<Double> col = result.get(j);
                    Random random = new Random();
                    for (int k = 0; k < col.size(); k++) {
                        double variation = random.nextGaussian() * 0.1;
                        double newValue = col.get(k) + variation;
                        // 使用当前设置的量表级数并限制范围
                        newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                        col.set(k, newValue);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 确保相关矩阵可逆
     */
    private List<List<Double>> ensureMatrixInvertibility(List<List<Double>> data) {
        // 通过添加微小的随机噪声来确保矩阵可逆
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        Random random = new Random();
        for (int i = 0; i < result.size(); i++) {
            List<Double> col = result.get(i);
            for (int j = 0; j < col.size(); j++) {
                // 添加极小的随机噪声
                double noise = random.nextGaussian() * 0.001;
                double newValue = col.get(j) + noise;
                // 使用当前设置的量表级数并限制范围
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                col.set(j, newValue);
            }
        }

        return result;
    }

    /**
     * 针对KMO目标调整数据
     */
    private List<List<Double>> adjustDataForKMOTarget(List<List<Double>> data, Double targetKMO, int iteration) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentKMO = calculateKMOFromAdjustedData(result);

        if (Double.isNaN(currentKMO) || currentKMO == 0.0) {
            return repairDataForKMO(result);
        }

        double kmoGap = targetKMO - currentKMO;

        if (kmoGap > 0) {
            // 需要提高KMO，增强变量间相关性
            return enhanceCorrelationsForKMOTarget(result, targetKMO);
        } else {
            // 需要降低KMO，减少变量间相关性
            return reduceCorrelationsForKMO(result, targetKMO);
        }
    }

    /**
     * 增强相关性以提高KMO - 专用版本
     */
    private List<List<Double>> enhanceCorrelationsForKMOTarget(List<List<Double>> data, double targetKMO) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 通过让变量更相似来增强相关性
        for (int i = 0; i < result.size(); i++) {
            for (int j = i + 1; j < result.size(); j++) {
                enhanceCorrelationBetweenItems(result.get(i), result.get(j), 0.1);
            }
        }

        return result;
    }

    /**
     * 减少相关性以降低KMO
     */
    private List<List<Double>> reduceCorrelationsForKMO(List<List<Double>> data, double targetKMO) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        // 通过添加独立的随机变异来减少相关性
        Random random = new Random();
        for (List<Double> col : result) {
            for (int i = 0; i < col.size(); i++) {
                if (random.nextDouble() < 0.3) {
                    double noise = random.nextGaussian() * 0.2;
                    double newValue = col.get(i) + noise;
                    // 使用当前设置的量表级数并限制范围
                    newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));
                    col.set(i, newValue);
                }
            }
        }

        return result;
    }

    /**
     * 计算两个变量间的相关系数
     */
    private double calculateCorrelation(List<Double> var1, List<Double> var2) {
        if (var1.size() != var2.size() || var1.size() < 2) {
            return 0.0;
        }

        try {
            double[] array1 = var1.stream().mapToDouble(Double::doubleValue).toArray();
            double[] array2 = var2.stream().mapToDouble(Double::doubleValue).toArray();

            PearsonsCorrelation correlation = new PearsonsCorrelation();
            double corr = correlation.correlation(array1, array2);

            return Double.isNaN(corr) ? 0.0 : corr;
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 智能调整维度间相关性
     */
    private List<List<Double>> adjustInterDimensionCorrelationsIntelligently(List<List<Double>> data,
                                                                            List<List<Integer>> dimensions,
                                                                            Double targetInterDimensionCorrelation) {
        log.info("[智能维度间相关性调整] 开始调整，目标相关性: {}", targetInterDimensionCorrelation);

        // 这里可以实现维度间相关性的智能调整
        // 暂时返回原数据，后续可以根据需要实现
        return data;
    }







    /**
     * 保守的维度调整策略
     * 使用最小化调整原则，避免破坏数据结构
     */
    private List<List<Double>> adjustSingleDimensionConservatively(List<List<Double>> data, double targetAlpha, double tolerance) {
        List<List<Double>> currentData = new ArrayList<>();
        for (List<Double> col : data) {
            currentData.add(new ArrayList<>(col));
        }

        // 在开始调整前确保数据变异性
        currentData = ensureDataVariability(currentData);

        double currentAlpha = calculateCronbachAlpha(currentData);
        double initialAlpha = currentAlpha;

        // 保存原始数据的分布特征
        List<Double> originalMeans = new ArrayList<>();
        List<Double> originalStds = new ArrayList<>();
        for (List<Double> col : data) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            col.forEach(stats::addValue);
            originalMeans.add(stats.getMean());
            originalStds.add(stats.getStandardDeviation());
        }

        log.info("[保守调整] 开始调整，初始信度: {:.3f}, 目标信度: {:.3f}", initialAlpha, targetAlpha);

        int maxIterations = 20; // 大幅减少迭代次数
        List<List<Double>> bestData = new ArrayList<>();
        for (List<Double> col : currentData) {
            bestData.add(new ArrayList<>(col));
        }
        double bestAlpha = currentAlpha;

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            double alphaGap = targetAlpha - currentAlpha;

            if (Math.abs(alphaGap) <= tolerance) {
                log.info("[保守调整] 第{}次迭代达到目标，当前信度: {:.3f}", iteration, currentAlpha);
                break;
            }

            // 执行一次微调
            List<List<Double>> newData = performMinimalAdjustment(currentData, targetAlpha, originalMeans, originalStds);

            // 关键：每次调整后都确保数据变异性
            newData = ensureDataVariability(newData);

            // 检查调整后的数据有效性
            if (!isDataValid(newData)) {
                log.warn("[保守调整] 第{}次迭代产生无效数据，尝试修复", iteration);
                newData = ensureDataVariability(newData);

                // 再次检查，如果仍然无效则停止
                if (!isDataValid(newData)) {
                    log.error("[保守调整] 第{}次迭代数据修复失败，停止调整", iteration);
                    break;
                }
            }

            double newAlpha = calculateCronbachAlpha(newData);

            // 检查是否有改进
            if (Math.abs(newAlpha - targetAlpha) < Math.abs(currentAlpha - targetAlpha)) {
                currentData = newData;
                currentAlpha = newAlpha;

                // 更新最佳结果
                if (Math.abs(newAlpha - targetAlpha) < Math.abs(bestAlpha - targetAlpha)) {
                    bestData = new ArrayList<>();
                    for (List<Double> col : newData) {
                        bestData.add(new ArrayList<>(col));
                    }
                    bestAlpha = newAlpha;
                }

                log.debug("[保守调整] 第{}次迭代成功，信度: {:.3f} -> {:.3f}", iteration, currentAlpha, newAlpha);
            } else {
                log.debug("[保守调整] 第{}次迭代无改进，信度变化: {:.3f} -> {:.3f}", iteration, currentAlpha, newAlpha);
                break; // 如果没有改进，停止调整
            }

            // 检查数据分布是否被过度破坏
            boolean distributionOk = true;
            for (int i = 0; i < currentData.size(); i++) {
                DescriptiveStatistics newStats = new DescriptiveStatistics();
                currentData.get(i).forEach(newStats::addValue);

                double newStd = newStats.getStandardDeviation();
                double originalStd = originalStds.get(i);

                if (newStd < originalStd * 0.5 || newStd > originalStd * 2.0) {
                    distributionOk = false;
                    break;
                }
            }

            if (!distributionOk) {
                log.warn("[保守调整] 第{}次迭代后数据分布被过度破坏，停止调整", iteration);
                break;
            }
        }

        log.info("[保守调整] 完成调整，{:.3f} -> {:.3f}，目标: {:.3f}",
                initialAlpha, bestAlpha, targetAlpha);

        // 最终检查：确保数据具有足够的变异性，防止KMO为NaN
        List<List<Double>> finalData = ensureDataVariability(bestData);

        // 验证最终数据的有效性
        if (!isDataValid(finalData)) {
            log.error("[保守调整] 最终数据无效，返回原始数据");
            return data;
        }

        return finalData;
    }

    /**
     * 执行最小化调整
     * 每次只对很少的数据点进行很小的调整
     */
    private List<List<Double>> performMinimalAdjustment(List<List<Double>> data, double targetAlpha,
                                                       List<Double> originalMeans, List<Double> originalStds) {
        List<List<Double>> result = new ArrayList<>();
        for (List<Double> col : data) {
            result.add(new ArrayList<>(col));
        }

        double currentAlpha = calculateCronbachAlpha(data);
        double alphaGap = targetAlpha - currentAlpha;

        if (Math.abs(alphaGap) < 0.001) {
            return result;
        }

        // 计算项目间相关性
        double[][] correlations = calculateItemCorrelations(data);

        int itemCount = data.size();
        int sampleSize = data.get(0).size();

        // 只调整很少的数据点
        int maxAdjustPoints = Math.max(1, sampleSize / 20); // 最多调整5%的数据点

        for (int i = 0; i < itemCount; i++) {
            List<Double> col = result.get(i);

            for (int adjustCount = 0; adjustCount < maxAdjustPoints; adjustCount++) {
                int randomIndex = (int) (Math.random() * sampleSize);
                double currentValue = col.get(randomIndex);

                // 计算调整方向
                double adjustment = 0.0;
                if (alphaGap > 0) {
                    // 需要提高信度，向其他项目的平均值靠拢
                    double otherAvg = 0.0;
                    double totalWeight = 0.0;

                    for (int j = 0; j < itemCount; j++) {
                        if (j != i) {
                            double weight = Math.abs(correlations[i][j]);
                            otherAvg += weight * data.get(j).get(randomIndex);
                            totalWeight += weight;
                        }
                    }

                    if (totalWeight > 0) {
                        otherAvg /= totalWeight;
                        adjustment = 0.05 * (otherAvg - currentValue); // 很小的调整幅度
                    }
                } else {
                    // 需要降低信度，添加很小的随机噪声
                    adjustment = (Math.random() - 0.5) * 0.1; // 很小的随机调整
                }

                // 限制调整幅度
                adjustment = Math.max(-0.1, Math.min(0.1, adjustment));

                double newValue = currentValue + adjustment;
                // 使用当前设置的量表级数并限制范围
                newValue = Math.max(1.0, Math.min((double)currentScaleLevel, newValue));

                col.set(randomIndex, newValue);
            }
        }

        return result;
    }








    /**
     * 计算项目间相关性矩阵
     */
    /**
     * 计算项目间相关性矩阵
     */
    private double[][] calculateItemCorrelations(List<List<Double>> data) {
        int itemCount = data.size();
        double[][] correlations = new double[itemCount][itemCount];

        PearsonsCorrelation correlation = new PearsonsCorrelation();

        for (int i = 0; i < itemCount; i++) {
            for (int j = 0; j < itemCount; j++) {
                if (i == j) {
                    correlations[i][j] = 1.0;
                } else {
                    try {
                        // 转换为数组
                        double[] array1 = data.get(i).stream().mapToDouble(Double::doubleValue).toArray();
                        double[] array2 = data.get(j).stream().mapToDouble(Double::doubleValue).toArray();

                        double corr = correlation.correlation(array1, array2);
                        correlations[i][j] = Double.isNaN(corr) ? 0.0 : corr;
                    } catch (Exception e) {
                        correlations[i][j] = 0.0;
                    }
                }
            }
        }

        return correlations;
    }

    /**
     * 计算Cronbach's Alpha系数 - 参考成功的实现
     */
    private double calculateCronbachAlpha(List<List<Double>> columnData) {
        if (columnData == null || columnData.isEmpty() || columnData.size() < 2) {
            log.warn("[信度计算] 数据无效或项目数少于2，返回0");
            return 0.0;
        }

        int itemCount = columnData.size();
        int sampleSize = columnData.get(0).size();

        // 验证所有列的样本数量一致
        for (List<Double> col : columnData) {
            if (col.size() != sampleSize) {
                log.warn("[信度计算] 数据列长度不一致，无法计算信度");
                return 0.0;
            }
        }

        // 检查数据有效性
        for (int i = 0; i < itemCount; i++) {
            List<Double> col = columnData.get(i);
            boolean hasValidData = col.stream().anyMatch(v -> v != null && !Double.isNaN(v) && Double.isFinite(v));
            if (!hasValidData) {
                log.warn("[信度计算] 第{}列没有有效数据", i + 1);
                return 0.0;
            }

            // 检查方差是否为0（所有值相同）
            DescriptiveStatistics colStats = new DescriptiveStatistics();
            col.forEach(value -> {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    colStats.addValue(value);
                }
            });

            if (colStats.getVariance() <= 1e-10) {
                log.warn("[信度计算] 第{}列方差为0（所有值相同），信度无法计算", i + 1);
                return 0.0;
            }
        }

        // 计算总分
        List<Double> totalScores = new ArrayList<>();
        for (int i = 0; i < sampleSize; i++) {
            double total = 0.0;
            int validCount = 0;
            for (List<Double> col : columnData) {
                Double value = col.get(i);
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    total += value;
                    validCount++;
                }
            }
            if (validCount > 0) {
                totalScores.add(total);
            }
        }

        if (totalScores.size() < 2) {
            log.warn("[信度计算] 有效样本数少于2，无法计算信度");
            return 0.0;
        }

        // 计算总方差
        DescriptiveStatistics totalStats = new DescriptiveStatistics();
        totalScores.forEach(totalStats::addValue);
        double totalVariance = totalStats.getVariance();

        // 计算各项目方差和
        double itemVarianceSum = 0.0;
        for (List<Double> col : columnData) {
            DescriptiveStatistics itemStats = new DescriptiveStatistics();
            col.forEach(value -> {
                if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                    itemStats.addValue(value);
                }
            });
            double variance = itemStats.getVariance();
            if (Double.isNaN(variance) || !Double.isFinite(variance)) {
                log.warn("[信度计算] 项目方差计算异常，返回0");
                return 0.0;
            }
            itemVarianceSum += variance;
        }

        // 计算Cronbach's Alpha
        if (totalVariance <= 1e-10 || Double.isNaN(totalVariance) || !Double.isFinite(totalVariance)) {
            log.warn("[信度计算] 总方差异常({})，返回0", totalVariance);
            return 0.0;
        }

        double alpha = (itemCount / (itemCount - 1.0)) * (1 - itemVarianceSum / totalVariance);

        // 检查计算结果
        if (Double.isNaN(alpha) || !Double.isFinite(alpha)) {
            log.warn("[信度计算] Alpha计算结果异常({})，返回0", alpha);
            return 0.0;
        }

        // 确保Alpha值在合理范围内
        double result = Math.max(0.0, Math.min(1.0, alpha));
        log.debug("[信度计算] 计算完成，Alpha={:.3f}", result);
        return result;
    }





    /**
     * 获取所有列号
     */
    private List<Integer> getAllColumns(List<List<Integer>> dimensions) {
        List<Integer> allColumns = new ArrayList<>();
        for (List<Integer> dimension : dimensions) {
            allColumns.addAll(dimension);
        }
        return allColumns;
    }

    /**
     * 获取数据索引
     */
    private int getDataIndex(Integer column, List<Integer> allColumns) {
        return allColumns.indexOf(column);
    }









    /**
     * 生成变更记录
     */
    private List<List<Object>> generateChangedCells(List<List<Double>> originalData,
            List<List<Double>> adjustedData, List<Integer> allColumns) {

        List<List<Object>> changedCells = new ArrayList<>();
        double threshold = 0.05; // 只有变化超过0.05才记录

        for (int colIdx = 0; colIdx < originalData.size(); colIdx++) {
            List<Double> originalCol = originalData.get(colIdx);
            List<Double> adjustedCol = adjustedData.get(colIdx);

            for (int rowIdx = 0; rowIdx < originalCol.size(); rowIdx++) {
                double originalValue = originalCol.get(rowIdx);
                double adjustedValue = adjustedCol.get(rowIdx);

                // 只有当变化足够大时才记录
                if (Math.abs(originalValue - adjustedValue) > threshold) {
                    // 根据原始数据的分布特征来决定最终值
                    String finalValue = generateReasonableValue(originalValue, adjustedValue, originalCol);

                    changedCells.add(Arrays.asList(
                        rowIdx + 1, // 行号（跳过表头）
                        allColumns.get(colIdx) - 1, // 列号（0基索引）
                        finalValue
                    ));
                }
            }
        }

        return changedCells;
    }

    /**
     * 根据原始数据分布生成合理的调整值
     */
    private String generateReasonableValue(double originalValue, double adjustedValue, List<Double> columnData) {
        // 使用当前设置的量表级数并确保调整后的值在合理范围内
        double finalValue = Math.max(1.0, Math.min((double)currentScaleLevel, adjustedValue));

        // 如果是量表数据，通常是整数值，但也可能有小数
        // 根据原始数据的特征来决定是否保留小数
        boolean hasDecimals = columnData.stream().anyMatch(v -> v % 1 != 0);

        if (hasDecimals) {
            // 如果原始数据有小数，保留一位小数
            return String.format("%.1f", finalValue);
        } else {
            // 如果原始数据都是整数，四舍五入到整数
            return String.valueOf(Math.round(finalValue));
        }
    }



    /**
     * 生成多维度调整说明 - 只包含表格中存在的数据
     */
    private String generateMultiDimensionalExplanation(List<List<Integer>> dimensions,
            List<Double> originalDimensionAlphas, List<Double> adjustedDimensionAlphas, List<Double> targetDimensionAlphas,
            Double originalTotalAlpha, Double adjustedTotalAlpha, Double targetTotalAlpha,
            Double originalKMO, Double adjustedKMO, Double targetKMO,
            int changedCellsCount, Map<String, Object> achievedMetrics) {

        StringBuilder explanation = new StringBuilder();
        explanation.append("成功完成分维度量表的信度效度调整：\n\n");

        // 各维度信度调整结果 - 只显示调整后的值、目标值、距离目标值和状态
        explanation.append("【维度信度调整结果】\n");
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);
            double adjustedAlpha = adjustedDimensionAlphas.get(i);
            double targetAlpha = targetDimensionAlphas.get(i);
            double distanceToTarget = adjustedAlpha - targetAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            explanation.append(String.format("维度%d（题目%s）：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                i + 1, dimension.toString(), adjustedAlpha, targetAlpha, distanceStr, status));
        }

        // 总量表信度调整结果
        if (targetTotalAlpha != null && adjustedTotalAlpha != null) {
            double distanceToTarget = adjustedTotalAlpha - targetTotalAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";
            explanation.append(String.format("\n【总量表信度】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                adjustedTotalAlpha, targetTotalAlpha, distanceStr, status));
        }

        // KMO值调整结果（效度指标）
        if (targetKMO != null && adjustedKMO != null) {
            double distanceToTarget = adjustedKMO - targetKMO;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";
            explanation.append(String.format("\n【效度指标KMO】：调整后%.3f，目标%.3f，距离目标值%s，%s\n",
                adjustedKMO, targetKMO, distanceStr, status));
        }

        // 调整统计信息 - 只显示表格中存在的统计数据
        explanation.append(String.format("\n【调整统计】\n"));
        explanation.append(String.format("- 调整单元格数量：%d个\n", changedCellsCount));
        explanation.append(String.format("- 涉及题目数量：%d个\n", getAllColumns(dimensions).size()));
        explanation.append(String.format("- 维度数量：%d个\n", dimensions.size()));

        // 质量评估 - 只显示表格中存在的达标比例
        explanation.append("\n【质量评估】\n");
        int passedDimensions = 0;
        for (int i = 0; i < adjustedDimensionAlphas.size(); i++) {
            if (Math.abs(adjustedDimensionAlphas.get(i) - targetDimensionAlphas.get(i)) <= 0.02) {
                passedDimensions++;
            }
        }
        explanation.append(String.format("- 达标维度比例：%d/%d\n", passedDimensions, dimensions.size()));

        explanation.append("\n调整完成，数据已按照目标要求进行优化。");

        return explanation.toString();
    }

    /**
     * 构建包含文字描述数据的表格摘要
     * 包含得分方向和均值变化信息
     */
    private Map<String, Object> buildSummaryMetrics(
            List<List<Integer>> dimensions,
            List<Double> originalDimensionAlphas, List<Double> adjustedDimensionAlphas, List<Double> targetDimensionAlphas,
            Double originalTotalAlpha, Double adjustedTotalAlpha, Double targetTotalAlpha,
            Double originalKMO, Double adjustedKMO, Double targetKMO,
            int changedCellsCount, List<List<Double>> targetItemMeans, List<List<String>> scoringDirections,
            List<List<Double>> adjustedData, List<Integer> allColumns) {

        Map<String, Object> metrics = new HashMap<>();

        // 基本信息
        metrics.put("dimensions", dimensions);
        metrics.put("cellsChanged", changedCellsCount);

        // 维度信度数据
        metrics.put("originalDimensionAlphas", originalDimensionAlphas);
        metrics.put("adjustedDimensionAlphas", adjustedDimensionAlphas);
        metrics.put("targetDimensionAlphas", targetDimensionAlphas);

        // 总量表信度数据
        if (originalTotalAlpha != null && adjustedTotalAlpha != null && targetTotalAlpha != null) {
            metrics.put("originalTotalAlpha", originalTotalAlpha);
            metrics.put("adjustedTotalAlpha", adjustedTotalAlpha);
            metrics.put("targetTotalAlpha", targetTotalAlpha);
        }

        // KMO数据
        if (originalKMO != null && adjustedKMO != null && targetKMO != null) {
            metrics.put("originalKMO", originalKMO);
            metrics.put("adjustedKMO", adjustedKMO);
            metrics.put("targetKMO", targetKMO);
        }

        // 构建详细的表格数据 - 包含所有调整信息
        List<List<Object>> summaryTableData = new ArrayList<>();

        // 添加维度信度行
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);
            double adjustedAlpha = adjustedDimensionAlphas.get(i);
            double targetAlpha = targetDimensionAlphas.get(i);
            double distanceToTarget = adjustedAlpha - targetAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String status = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "维度" + (i + 1) + "信度（题目" + dimension.toString() + "）",
                String.format("%.3f", adjustedAlpha),
                String.format("%.3f", targetAlpha),
                distanceStr,
                status
            ));
        }

        // 添加得分方向设置信息
        if (scoringDirections != null && !scoringDirections.isEmpty()) {
            for (int i = 0; i < dimensions.size(); i++) {
                List<Integer> dimension = dimensions.get(i);
                List<String> dimDirections = scoringDirections.get(i);

                // 转换为中文显示
                String directionInfo = dimDirections.stream()
                    .map(dir -> "positive".equals(dir) ? "正向" : "反向")
                    .collect(java.util.stream.Collectors.joining(","));

                summaryTableData.add(Arrays.asList(
                    "维度" + (i + 1) + "得分方向（题目" + dimension.toString() + "）",
                    "-",
                    "-",
                    "-",
                    directionInfo
                ));
            }
        }

        // 添加题目均值信息（显示调整后的实际均值）
        for (int i = 0; i < dimensions.size(); i++) {
            List<Integer> dimension = dimensions.get(i);

            // 显示每个题目的实际均值（基于调整后但未反向转换的数据）
            for (int j = 0; j < dimension.size(); j++) {
                Integer col = dimension.get(j);
                String direction = scoringDirections != null ? scoringDirections.get(i).get(j) : "positive";

                // 计算调整后的实际均值（使用未反向转换的数据）
                int dataIdx = getDataIndex(col, allColumns);
                double actualMean = 0.0;
                if (dataIdx >= 0 && dataIdx < adjustedData.size()) {
                    List<Double> colData = adjustedData.get(dataIdx);
                    actualMean = colData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                }

                // 获取目标均值（如果有设置）
                Double targetMean = null;
                if (targetItemMeans != null && i < targetItemMeans.size() && j < targetItemMeans.get(i).size()) {
                    targetMean = targetItemMeans.get(i).get(j);
                }

                // 计算距离目标值的差距
                String distanceStr = "-";
                String status = direction.equals("negative") ? "反向化" : "正向";
                if (targetMean != null) {
                    double distance = actualMean - targetMean;
                    distanceStr = distance >= 0 ? String.format("+%.3f", distance) : String.format("%.3f", distance);
                    status = Math.abs(distance) <= 0.1 ? "✓达标" : "需优化";
                }

                summaryTableData.add(Arrays.asList(
                    String.format("题目%d均值（%s计分）", col, direction.equals("negative") ? "反向" : "正向"),
                    String.format("%.3f", actualMean),
                    targetMean != null ? String.format("%.3f", targetMean) : "-",
                    distanceStr,
                    status
                ));
            }
        }

        // 添加总量表信度行
        if (adjustedTotalAlpha != null && targetTotalAlpha != null) {
            double distanceToTarget = adjustedTotalAlpha - targetTotalAlpha;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String totalStatus = Math.abs(distanceToTarget) <= 0.02 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "总量表信度（全部题目）",
                String.format("%.3f", adjustedTotalAlpha),
                String.format("%.3f", targetTotalAlpha),
                distanceStr,
                totalStatus
            ));
        }

        // 添加KMO行
        if (adjustedKMO != null && targetKMO != null) {
            double distanceToTarget = adjustedKMO - targetKMO;
            String distanceStr = distanceToTarget >= 0 ? String.format("+%.3f", distanceToTarget) : String.format("%.3f", distanceToTarget);
            String kmoStatus = Math.abs(distanceToTarget) <= 0.05 ? "✓达标" : "需优化";

            summaryTableData.add(Arrays.asList(
                "KMO效度指标",
                String.format("%.3f", adjustedKMO),
                String.format("%.3f", targetKMO),
                distanceStr,
                kmoStatus
            ));
        }

        // 添加调整统计信息行
        summaryTableData.add(Arrays.asList(
            "调整单元格数量",
            "-",
            "-",
            "-",
            String.valueOf(changedCellsCount) + "个"
        ));

        summaryTableData.add(Arrays.asList(
            "涉及题目数量",
            "-",
            "-",
            "-",
            String.valueOf(getAllColumns(dimensions).size()) + "个"
        ));

        summaryTableData.add(Arrays.asList(
            "维度数量",
            "-",
            "-",
            "-",
            String.valueOf(dimensions.size()) + "个"
        ));

        // 计算达标情况
        int passedDimensions = 0;
        for (int i = 0; i < adjustedDimensionAlphas.size(); i++) {
            if (Math.abs(adjustedDimensionAlphas.get(i) - targetDimensionAlphas.get(i)) <= 0.02) {
                passedDimensions++;
            }
        }

        summaryTableData.add(Arrays.asList(
            "达标维度比例",
            "-",
            "-",
            "-",
            passedDimensions + "/" + dimensions.size()
        ));

        metrics.put("summaryTableData", summaryTableData);
        metrics.put("summaryTableHeaders", Arrays.asList("指标", "调整后", "目标值", "距离目标值", "状态"));

        return metrics;
    }

    /**
     * 计算KMO值 - 参考AdjustDataTools的正确实现
     */
    private double calculateKMOFromData(String sessionId, List<Integer> columns) {
        try {
            // 获取数值数据
            List<List<Double>> columnData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = baseTools.getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    log.warn("[KMO计算] 第{}列没有有效的数值数据", col);
                    return 0.0;
                }
                columnData.add(data);
            }

            if (columnData.size() < 2) {
                log.warn("[KMO计算] 需要至少2个变量才能计算KMO");
                return 0.0;
            }

            // 标准化数据
            List<List<Double>> standardizedData = standardizeData(columnData);

            // 计算相关矩阵
            RealMatrix correlationMatrix = calculateCorrelationMatrix(standardizedData);

            // 计算KMO测度
            return calculateKMOMeasure(correlationMatrix);

        } catch (Exception e) {
            log.error("[KMO计算] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 标准化数据
     */
    private List<List<Double>> standardizeData(List<List<Double>> data) {
        List<List<Double>> standardized = new ArrayList<>();

        for (List<Double> column : data) {
            DescriptiveStatistics stats = new DescriptiveStatistics();
            column.forEach(stats::addValue);

            double mean = stats.getMean();
            double std = stats.getStandardDeviation();

            List<Double> standardizedColumn = new ArrayList<>();
            for (Double value : column) {
                if (std == 0) {
                    standardizedColumn.add(0.0);
                } else {
                    standardizedColumn.add((value - mean) / std);
                }
            }
            standardized.add(standardizedColumn);
        }

        return standardized;
    }

    /**
     * 计算相关矩阵
     */
    private RealMatrix calculateCorrelationMatrix(List<List<Double>> data) {
        int variableCount = data.size();
        int sampleSize = data.get(0).size();

        double[][] dataArray = new double[sampleSize][variableCount];
        for (int i = 0; i < sampleSize; i++) {
            for (int j = 0; j < variableCount; j++) {
                dataArray[i][j] = data.get(j).get(i);
            }
        }

        return new PearsonsCorrelation().computeCorrelationMatrix(dataArray);
    }

    /**
     * 计算KMO测度 - 参考AdjustDataTools的实现
     */
    private double calculateKMOMeasure(RealMatrix correlationMatrix) {
        int n = correlationMatrix.getRowDimension();
        RealMatrix inverseCorrelationMatrix;

        try {
            inverseCorrelationMatrix = new LUDecomposition(correlationMatrix).getSolver().getInverse();
        } catch (Exception e) {
            log.warn("[KMO计算] 无法计算相关矩阵的逆矩阵，可能存在共线性问题，KMO返回0.0");
            return 0.0;
        }

        double sumOfSquaredCorrelations = 0.0;
        double sumOfSquaredPartialCorrelations = 0.0;

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i == j) continue;

                // 计算简单相关系数的平方和
                sumOfSquaredCorrelations += Math.pow(correlationMatrix.getEntry(i, j), 2);

                // 计算偏相关系数的平方和
                double partialCorrelation = -inverseCorrelationMatrix.getEntry(i, j) /
                        Math.sqrt(inverseCorrelationMatrix.getEntry(i, i) * inverseCorrelationMatrix.getEntry(j, j));
                sumOfSquaredPartialCorrelations += Math.pow(partialCorrelation, 2);
            }
        }

        // KMO = (sum_r^2) / (sum_r^2 + sum_u^2)
        double numerator = sumOfSquaredCorrelations;
        double denominator = sumOfSquaredCorrelations + sumOfSquaredPartialCorrelations;

        if (denominator == 0) {
            return 0.0;
        } else {
            return numerator / denominator;
        }
    }

    /**
     * 基于调整后的数据计算KMO值 - 加强版，防止NaN
     */
    private double calculateKMOFromAdjustedData(List<List<Double>> adjustedData) {
        try {
            if (adjustedData == null || adjustedData.isEmpty() || adjustedData.size() < 2) {
                log.warn("[KMO计算] 需要至少2个变量才能计算KMO");
                return 0.0;
            }

            // 关键：在计算KMO之前，强制确保数据变异性
            List<List<Double>> safeData = ensureDataVariability(adjustedData);

            // 更严格的数据有效性检查
            for (int i = 0; i < safeData.size(); i++) {
                List<Double> col = safeData.get(i);
                if (col == null || col.isEmpty()) {
                    log.warn("[KMO计算] 第{}列数据为空", i + 1);
                    return 0.0;
                }

                // 检查是否所有值都相同（方差为0）- 更严格的阈值
                DescriptiveStatistics stats = new DescriptiveStatistics();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                }

                // 更严格的变异性要求
                if (stats.getN() < 3 || stats.getVariance() <= 1e-3 || stats.getStandardDeviation() <= 0.05) {
                    log.warn("[KMO计算] 第{}列变异性不足(N={}, 方差={:.6f}, 标准差={:.6f})，强制修复",
                            i + 1, stats.getN(), stats.getVariance(), stats.getStandardDeviation());
                    // 如果仍然有问题，返回一个安全的默认值
                    return 0.5;
                }

                // 检查唯一值数量
                Set<Double> uniqueValues = new HashSet<>();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        uniqueValues.add(Math.round(value * 100.0) / 100.0);
                    }
                }

                if (uniqueValues.size() < 3) {
                    log.warn("[KMO计算] 第{}列只有{}个不同值，变异性不足", i + 1, uniqueValues.size());
                    return 0.5;
                }
            }

            // 使用修复后的数据进行后续计算
            adjustedData = safeData;

            // 标准化数据
            List<List<Double>> standardizedData = standardizeDataSafely(adjustedData);
            if (standardizedData == null) {
                log.warn("[KMO计算] 数据标准化失败");
                return 0.0;
            }

            // 计算相关矩阵
            RealMatrix correlationMatrix = calculateCorrelationMatrixSafely(standardizedData);
            if (correlationMatrix == null) {
                log.warn("[KMO计算] 相关矩阵计算失败");
                return 0.0;
            }

            // 计算KMO测度
            double kmo = calculateKMOMeasureSafely(correlationMatrix);
            log.debug("[KMO计算] 计算完成，KMO={:.3f}", kmo);
            return kmo;

        } catch (Exception e) {
            log.error("[KMO计算] 基于调整数据计算失败", e);
            return 0.0;
        }
    }

    /**
     * 安全的数据标准化
     */
    private List<List<Double>> standardizeDataSafely(List<List<Double>> data) {
        try {
            List<List<Double>> result = new ArrayList<>();

            for (List<Double> col : data) {
                DescriptiveStatistics stats = new DescriptiveStatistics();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        stats.addValue(value);
                    }
                }

                double mean = stats.getMean();
                double std = stats.getStandardDeviation();

                if (std <= 1e-10 || Double.isNaN(std) || !Double.isFinite(std)) {
                    log.warn("[数据标准化] 标准差异常，无法标准化");
                    return null;
                }

                List<Double> standardizedCol = new ArrayList<>();
                for (Double value : col) {
                    if (value != null && !Double.isNaN(value) && Double.isFinite(value)) {
                        double standardized = (value - mean) / std;
                        if (Double.isNaN(standardized) || !Double.isFinite(standardized)) {
                            standardizedCol.add(0.0);
                        } else {
                            standardizedCol.add(standardized);
                        }
                    } else {
                        standardizedCol.add(0.0);
                    }
                }
                result.add(standardizedCol);
            }

            return result;
        } catch (Exception e) {
            log.error("[数据标准化] 标准化失败", e);
            return null;
        }
    }

    /**
     * 安全的相关矩阵计算
     */
    private RealMatrix calculateCorrelationMatrixSafely(List<List<Double>> data) {
        try {
            PearsonsCorrelation correlation = new PearsonsCorrelation();
            int n = data.size();
            double[][] corrMatrix = new double[n][n];

            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i == j) {
                        corrMatrix[i][j] = 1.0;
                    } else {
                        try {
                            double[] array1 = data.get(i).stream().mapToDouble(Double::doubleValue).toArray();
                            double[] array2 = data.get(j).stream().mapToDouble(Double::doubleValue).toArray();

                            double corr = correlation.correlation(array1, array2);
                            if (Double.isNaN(corr) || !Double.isFinite(corr)) {
                                corrMatrix[i][j] = 0.0;
                            } else {
                                corrMatrix[i][j] = corr;
                            }
                        } catch (Exception e) {
                            corrMatrix[i][j] = 0.0;
                        }
                    }
                }
            }

            return new Array2DRowRealMatrix(corrMatrix);
        } catch (Exception e) {
            log.error("[相关矩阵计算] 计算失败", e);
            return null;
        }
    }

    /**
     * 安全的KMO测度计算
     */
    private double calculateKMOMeasureSafely(RealMatrix correlationMatrix) {
        try {
            int n = correlationMatrix.getRowDimension();
            if (n < 2) {
                return 0.0;
            }

            // 计算反相关矩阵
            RealMatrix inverseMatrix;
            try {
                LUDecomposition lu = new LUDecomposition(correlationMatrix);
                if (lu.getDeterminant() == 0) {
                    log.warn("[KMO计算] 相关矩阵不可逆");
                    return 0.0;
                }
                inverseMatrix = lu.getSolver().getInverse();
            } catch (Exception e) {
                log.warn("[KMO计算] 矩阵求逆失败");
                return 0.0;
            }

            double sumR2 = 0.0;
            double sumA2 = 0.0;

            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i != j) {
                        double r = correlationMatrix.getEntry(i, j);
                        double a = -inverseMatrix.getEntry(i, j) / Math.sqrt(inverseMatrix.getEntry(i, i) * inverseMatrix.getEntry(j, j));

                        if (Double.isFinite(r)) {
                            sumR2 += r * r;
                        }
                        if (Double.isFinite(a)) {
                            sumA2 += a * a;
                        }
                    }
                }
            }

            if (sumR2 + sumA2 <= 1e-10) {
                return 0.0;
            }

            double kmo = sumR2 / (sumR2 + sumA2);

            if (Double.isNaN(kmo) || !Double.isFinite(kmo)) {
                return 0.0;
            }

            return Math.max(0.0, Math.min(1.0, kmo));

        } catch (Exception e) {
            log.error("[KMO测度计算] 计算失败", e);
            return 0.0;
        }
    }

    /**
     * 按正向计分调整题目均值（新的统一处理方法）
     * 现在所有题目都已经转换为正向计分，直接调整即可
     */
    private List<Double> adjustItemMeanAsPositive(List<Double> originalData, Double targetMean) {
        if (targetMean == null) {
            return new ArrayList<>(originalData);
        }

        log.info("[均值调整] 题目当前均值: {:.3f}, 目标均值: {:.3f}",
                originalData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0), targetMean);

        // 直接按正向计分调整
        return adjustPositiveScoreItem(originalData, targetMean);
    }

    /**
     * 根据得分方向调整题目均值
     * 统一处理策略：先转换为正常得分，调整后再转换回选项值
     * 关键理解：单元格存储的是选项序号，不是得分值
     * 注意：这个方法现在已经不再使用，保留用于兼容性
     */
    @Deprecated
    private List<Double> adjustItemMeanWithDirection(List<Double> originalData, Double targetMean, String direction) {
        if (targetMean == null) {
            return new ArrayList<>(originalData);
        }

        // 计算当前得分均值（考虑得分方向）
        double currentScoreMean = calculateScoreMean(originalData, direction);

        log.info("[均值调整] 题目当前得分均值: {:.3f}, 目标得分均值: {}, 得分方向: {}",
                currentScoreMean, targetMean, direction);

        // 统一的容差标准
        double tolerance = 0.05;
        if (Math.abs(currentScoreMean - targetMean) <= tolerance) {
            return new ArrayList<>(originalData);
        }

        // 统一处理策略：将选项值转换为正常得分进行调整
        List<Double> adjustedData;
        if ("negative".equals(direction)) {
            // 反向计分：先转换为正常得分，调整后再转换回选项值
            adjustedData = adjustNegativeScoreItem(originalData, targetMean);
        } else {
            // 正向计分：直接调整（选项值就是得分值）
            adjustedData = adjustPositiveScoreItem(originalData, targetMean);
        }

        // 验证调整后的均值
        double actualScoreMean = calculateScoreMean(adjustedData, direction);
        log.info("[均值调整] 调整后实际得分均值: {:.3f}", actualScoreMean);

        return adjustedData;
    }

    /**
     * 调整正向计分题目（选项值就是得分值）
     * 改进版本：更精确的均值调整算法
     */
    private List<Double> adjustPositiveScoreItem(List<Double> originalData, double targetMean) {
        List<Double> adjustedData = new ArrayList<>(originalData);

        // 检测数据范围
        double maxOption = originalData.stream().mapToDouble(Double::doubleValue).max().orElse(5.0);
        double minOption = originalData.stream().mapToDouble(Double::doubleValue).min().orElse(1.0);

        log.info("[正向计分调整] 检测到量表范围: {}-{}, 目标均值: {}", minOption, maxOption, targetMean);

        // 使用保守的均值调整策略
        return adjustMeanConservatively(adjustedData, targetMean, minOption, maxOption);
    }

    /**
     * 保守的均值调整策略
     * 使用最小化调整原则，避免破坏数据分布
     */
    private List<Double> adjustMeanConservatively(List<Double> data, double targetMean, double minOption, double maxOption) {
        List<Double> currentData = new ArrayList<>(data);

        double currentMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double initialMean = currentMean;

        log.info("[保守均值调整] 开始调整，初始均值: {:.3f}, 目标均值: {:.3f}", initialMean, targetMean);

        // 如果已经接近目标，不调整
        if (Math.abs(currentMean - targetMean) <= 0.05) {
            log.info("[保守均值调整] 当前均值已接近目标，无需调整");
            return currentData;
        }

        // 保存原始分布特征
        DescriptiveStatistics originalStats = new DescriptiveStatistics();
        currentData.forEach(originalStats::addValue);
        double originalStd = originalStats.getStandardDeviation();

        int maxIterations = 15; // 减少迭代次数
        double tolerance = 0.05; // 放宽容差

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            currentMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double meanGap = targetMean - currentMean;

            if (Math.abs(meanGap) <= tolerance) {
                log.info("[保守均值调整] 第{}次迭代达到目标，当前均值: {:.3f}", iteration, currentMean);
                break;
            }

            // 执行微调
            currentData = performMinimalMeanAdjustment(currentData, targetMean, minOption, maxOption, originalStd);

            // 检查分布是否被过度破坏
            DescriptiveStatistics newStats = new DescriptiveStatistics();
            currentData.forEach(newStats::addValue);
            double newStd = newStats.getStandardDeviation();

            if (newStd < originalStd * 0.6 || newStd > originalStd * 1.5) {
                log.warn("[保守均值调整] 第{}次迭代后标准差变化过大({:.3f} -> {:.3f})，停止调整",
                        iteration, originalStd, newStd);
                break;
            }

            double newMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            log.debug("[保守均值调整] 第{}次迭代，均值: {:.3f} -> {:.3f}", iteration, currentMean, newMean);
        }

        double finalMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[保守均值调整] 完成调整，{:.3f} -> {:.3f}，目标: {:.3f}",
                initialMean, finalMean, targetMean);

        return currentData;
    }

    /**
     * 执行最小化均值调整
     * 每次只调整很少的数据点
     */
    private List<Double> performMinimalMeanAdjustment(List<Double> data, double targetMean,
                                                     double minOption, double maxOption, double originalStd) {
        List<Double> result = new ArrayList<>(data);

        double currentMean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double difference = targetMean - currentMean;

        if (Math.abs(difference) < 0.01) {
            return result;
        }

        // 智能选择调整点：优先选择距离边界远的点
        List<Integer> candidateIndices = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            if (difference > 0 && value < maxOption - 0.5) {
                candidateIndices.add(i);
            } else if (difference < 0 && value > minOption + 0.5) {
                candidateIndices.add(i);
            }
        }

        if (candidateIndices.isEmpty()) {
            return result;
        }

        // 按照距离边界的远近排序
        candidateIndices.sort((i1, i2) -> {
            double val1 = data.get(i1);
            double val2 = data.get(i2);
            if (difference > 0) {
                return Double.compare(maxOption - val2, maxOption - val1);
            } else {
                return Double.compare(val2 - minOption, val1 - minOption);
            }
        });

        // 只调整很少的数据点
        int adjustCount = Math.max(1, Math.min(candidateIndices.size(), data.size() / 10)); // 最多调整10%
        double adjustmentPerPoint = 0.2; // 很小的调整幅度

        for (int i = 0; i < adjustCount; i++) {
            int index = candidateIndices.get(i);
            double currentValue = result.get(index);
            double adjustment = difference > 0 ? adjustmentPerPoint : -adjustmentPerPoint;

            double newValue = currentValue + adjustment;
            newValue = Math.max(minOption, Math.min(maxOption, Math.round(newValue * 10.0) / 10.0)); // 保留一位小数
            result.set(index, newValue);
        }

        return result;
    }

    /**
     * 实时反馈的均值调整算法
     * 在调整过程中实时监控结果，动态调整策略
     */
    private List<Double> adjustMeanWithRealTimeFeedback(List<Double> data, double targetMean, double minOption, double maxOption) {
        List<Double> currentData = new ArrayList<>(data);
        int maxIterations = 30;
        double tolerance = 0.03;

        double currentMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double initialMean = currentMean;

        log.info("[实时反馈均值调整] 开始调整，初始均值: {:.3f}, 目标均值: {:.3f}", initialMean, targetMean);

        // 动态调整参数
        MeanAdjustmentParams params = new MeanAdjustmentParams(currentMean, targetMean, data.size());

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            currentMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double meanGap = targetMean - currentMean;

            if (Math.abs(meanGap) <= tolerance) {
                log.info("[实时反馈均值调整] 第{}次迭代达到目标，当前均值: {:.3f}", iteration, currentMean);
                break;
            }

            // 实时调整策略参数
            params.updateForIteration(iteration, currentMean, targetMean);

            // 执行调整
            List<Double> newData = adjustMeanStep(currentData, targetMean, minOption, maxOption, params);

            double newMean = newData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            // 实时评估调整效果
            boolean shouldAccept = evaluateMeanAdjustmentEffect(currentMean, newMean, targetMean, params);

            if (shouldAccept) {
                currentData = newData;
                params.recordSuccess();
                log.debug("[实时反馈均值调整] 第{}次迭代成功，均值: {:.3f} -> {:.3f}", iteration, currentMean, newMean);
            } else {
                params.recordFailure();
                log.debug("[实时反馈均值调整] 第{}次迭代被拒绝，均值变化: {:.3f} -> {:.3f}", iteration, currentMean, newMean);
            }

            // 如果连续失败，调整策略
            if (params.shouldAdjustStrategy()) {
                params.adjustStrategy();
                log.debug("[实时反馈均值调整] 策略调整，新强度: {:.3f}", params.getAdjustmentStrength());
            }
        }

        double finalMean = currentData.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[实时反馈均值调整] 完成调整，{:.3f} -> {:.3f}，目标: {:.3f}",
                initialMean, finalMean, targetMean);

        return currentData;
    }

    /**
     * 均值调整参数类
     */
    private static class MeanAdjustmentParams {
        private double adjustmentStrength;
        private double initialGap;
        private int successCount = 0;
        private int failureCount = 0;
        private int consecutiveFailures = 0;
        private int dataSize;

        public MeanAdjustmentParams(double currentMean, double targetMean, int dataSize) {
            this.initialGap = Math.abs(targetMean - currentMean);
            this.adjustmentStrength = Math.min(1.0, Math.max(0.3, initialGap * 0.8));
            this.dataSize = dataSize;
        }

        public void updateForIteration(int iteration, double currentMean, double targetMean) {
            // 根据迭代次数和进展动态调整强度
            if (iteration > 10 && Math.abs(currentMean - targetMean) > initialGap * 0.8) {
                adjustmentStrength = Math.min(1.5, adjustmentStrength * 1.1);
            }
        }

        public void recordSuccess() {
            successCount++;
            consecutiveFailures = 0;
        }

        public void recordFailure() {
            failureCount++;
            consecutiveFailures++;
        }

        public boolean shouldAdjustStrategy() {
            return consecutiveFailures >= 3;
        }

        public void adjustStrategy() {
            adjustmentStrength *= 0.7;
            adjustmentStrength = Math.max(0.1, adjustmentStrength);
            consecutiveFailures = 0;
        }

        public double getAdjustmentStrength() {
            return adjustmentStrength;
        }

        public int getAdjustmentCount() {
            // 根据成功率动态调整数据点数量
            double successRate = successCount > 0 ? (double) successCount / (successCount + failureCount) : 0.5;
            int baseCount = Math.max(1, Math.min(dataSize / 4, 8));
            return (int) (baseCount * Math.max(0.5, successRate));
        }
    }

    /**
     * 执行一步均值调整
     */
    private List<Double> adjustMeanStep(List<Double> data, double targetMean, double minOption, double maxOption, MeanAdjustmentParams params) {
        List<Double> result = new ArrayList<>(data);

        double currentMean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double difference = targetMean - currentMean;

        if (Math.abs(difference) < 0.01) {
            return result;
        }

        // 智能选择调整点
        List<Integer> candidateIndices = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            double value = data.get(i);
            if (difference > 0 && value < maxOption) {
                candidateIndices.add(i);
            } else if (difference < 0 && value > minOption) {
                candidateIndices.add(i);
            }
        }

        if (candidateIndices.isEmpty()) {
            return result;
        }

        // 按照距离边界的远近排序
        candidateIndices.sort((i1, i2) -> {
            double val1 = data.get(i1);
            double val2 = data.get(i2);
            if (difference > 0) {
                return Double.compare(maxOption - val2, maxOption - val1);
            } else {
                return Double.compare(val2 - minOption, val1 - minOption);
            }
        });

        // 调整数据点
        int adjustCount = Math.min(candidateIndices.size(), params.getAdjustmentCount());
        double adjustmentPerPoint = params.getAdjustmentStrength();

        for (int i = 0; i < adjustCount; i++) {
            int index = candidateIndices.get(i);
            double currentValue = result.get(index);
            double adjustment = difference > 0 ? adjustmentPerPoint : -adjustmentPerPoint;

            double newValue = currentValue + adjustment;
            newValue = Math.max(minOption, Math.min(maxOption, Math.round(newValue)));
            result.set(index, newValue);
        }

        return result;
    }

    /**
     * 评估均值调整效果
     */
    private boolean evaluateMeanAdjustmentEffect(double oldMean, double newMean, double targetMean, MeanAdjustmentParams params) {
        double oldDistance = Math.abs(oldMean - targetMean);
        double newDistance = Math.abs(newMean - targetMean);

        // 如果更接近目标，接受
        if (newDistance < oldDistance) {
            return true;
        }

        // 如果距离相近但在可接受范围内，也接受
        if (newDistance <= oldDistance * 1.05 && newDistance < 0.1) {
            return true;
        }

        return false;
    }

    /**
     * 调整反向计分题目（先转换为正常得分，调整后再转换回选项值）
     * 修复逻辑：确保反向计分题的均值调整正确处理选项值与得分值的转换
     */
    private List<Double> adjustNegativeScoreItem(List<Double> originalData, double targetMean) {
        // 检测数据范围
        double maxOption = originalData.stream().mapToDouble(Double::doubleValue).max().orElse(5.0);
        double minOption = originalData.stream().mapToDouble(Double::doubleValue).min().orElse(1.0);

        log.info("[反向计分调整] 检测到量表范围: {}-{}, 目标得分均值: {}", minOption, maxOption, targetMean);

        // 第1步：将反向计分的选项值转换为正常得分
        List<Double> normalScores = new ArrayList<>();
        for (Double option : originalData) {
            if (option != null) {
                double normalScore = (maxOption + minOption) - option;
                normalScores.add(normalScore);
            }
        }

        double originalNormalMean = normalScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[反向计分调整] 原始选项值转换为正常得分后的均值: {:.3f}, 目标得分均值: {:.3f}", originalNormalMean, targetMean);

        // 第2步：按照正常得分进行调整（目标是targetMean）
        // 这里调整的是得分值，不是选项值
        List<Double> adjustedScores = adjustScoreValues(normalScores, targetMean, minOption, maxOption);

        double adjustedNormalMean = adjustedScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[反向计分调整] 调整后正常得分均值: {:.3f}", adjustedNormalMean);

        // 第3步：将调整后的正常得分转换回反向计分的选项值
        List<Double> adjustedOptions = new ArrayList<>();
        for (Double score : adjustedScores) {
            if (score != null) {
                double option = (maxOption + minOption) - score;
                // 确保选项值在合理范围内
                option = Math.max(minOption, Math.min(maxOption, option));
                adjustedOptions.add(option);
            }
        }

        // 验证最终结果
        double finalScoreMean = calculateScoreMean(adjustedOptions, "negative");
        log.info("[反向计分调整] 最终反向计分得分均值: {:.3f}", finalScoreMean);

        // 验证选项值分布
        Map<Double, Long> optionCounts = adjustedOptions.stream()
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        log.info("[反向计分调整] 调整后选项值分布: {}", optionCounts);

        return adjustedOptions;
    }

    /**
     * 调整得分值以达到目标均值（专门用于反向计分题的得分调整）
     * 这个方法直接调整得分值，不涉及选项值转换
     */
    private List<Double> adjustScoreValues(List<Double> scoreData, double targetMean, double minOption, double maxOption) {
        List<Double> adjustedScores = new ArrayList<>(scoreData);

        // 计算得分的有效范围（对于反向计分，得分范围和选项范围相同）
        double minScore = minOption;
        double maxScore = maxOption;

        // 使用迭代调整策略
        int maxIterations = 200;
        double tolerance = 0.02;
        Random random = new Random();

        for (int iteration = 0; iteration < maxIterations; iteration++) {
            double currentMean = adjustedScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double difference = targetMean - currentMean;

            if (Math.abs(difference) <= tolerance) {
                log.info("[得分调整] 第{}次迭代达到目标，当前均值: {:.3f}", iteration, currentMean);
                break;
            }

            // 计算需要调整的数据点数量
            int adjustCount = Math.max(1, Math.min(adjustedScores.size() / 5, 20));

            for (int i = 0; i < adjustCount; i++) {
                int randomIndex = random.nextInt(adjustedScores.size());
                double currentScore = adjustedScores.get(randomIndex);

                if (difference > 0 && currentScore < maxScore) {
                    // 需要提高均值，增加得分
                    double newScore = Math.min(maxScore, currentScore + 1.0);
                    adjustedScores.set(randomIndex, newScore);
                } else if (difference < 0 && currentScore > minScore) {
                    // 需要降低均值，减少得分
                    double newScore = Math.max(minScore, currentScore - 1.0);
                    adjustedScores.set(randomIndex, newScore);
                }
            }

            if (iteration % 50 == 0) {
                double iterMean = adjustedScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                log.debug("[得分调整] 第{}次迭代，当前均值: {:.3f}, 目标: {:.3f}", iteration, iterMean, targetMean);
            }
        }

        double finalMean = adjustedScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        log.info("[得分调整] 调整完成，最终得分均值: {:.3f}, 目标: {:.3f}", finalMean, targetMean);

        return adjustedScores;
    }



    /**
     * 计算得分均值（考虑得分方向）
     * 支持各种级数的量表（自动检测最大值）
     */
    private double calculateScoreMean(List<Double> optionData, String direction) {
        double totalScore = 0.0;
        int count = 0;

        // 自动检测量表的最大值（级数）
        double maxOption = 0.0;
        double minOption = Double.MAX_VALUE;
        for (Double option : optionData) {
            if (option != null && !Double.isNaN(option)) {
                maxOption = Math.max(maxOption, option);
                minOption = Math.min(minOption, option);
            }
        }

        // 如果没有有效数据，返回0
        if (maxOption == 0.0 || minOption == Double.MAX_VALUE) {
            return 0.0;
        }

        for (Double option : optionData) {
            if (option != null && option >= minOption && option <= maxOption) {
                double score;
                if ("negative".equals(direction)) {
                    // 反向计分：选项值越小，得分越高
                    // 公式：得分 = (最大值 + 最小值) - 选项值
                    // 例如：5点量表(1-5)：选项1=5分，选项5=1分
                    // 例如：7点量表(1-7)：选项1=7分，选项7=1分
                    // 例如：4点量表(1-4)：选项1=4分，选项4=1分
                    score = (maxOption + minOption) - option;
                } else {
                    // 正向计分：选项值就是得分值
                    score = option;
                }
                totalScore += score;
                count++;
            }
        }

        return count > 0 ? totalScore / count : 0.0;
    }



    /**
     * 获取题目的量表级数（优先从缓存获取，否则使用默认值）
     * @param questionNum 题目编号
     * @param data 题目数据（暂时不使用，保留接口兼容性）
     * @return 量表级数
     */
    private Integer getQuestionScaleLevel(Integer questionNum, List<Double> data) {
        // 优先从缓存获取
        Integer scaleLevel = questionScaleLevelsCache.get(questionNum);
        if (scaleLevel != null) {
            return scaleLevel;
        }

        // 如果缓存中没有，使用当前设置的量表级数
        scaleLevel = currentScaleLevel;

        // 将默认值保存到缓存中
        questionScaleLevelsCache.put(questionNum, scaleLevel);

        return scaleLevel;
    }







    /**
     * 生成分维度调整量表的配置文本JSON
     */
    private String generateConfigText(List<List<Integer>> dimensions, Integer scaleLevel, List<Double> targetDimensionAlphas,
                                    Double targetTotalAlpha, Double targetKMO, Double targetInterDimensionCorrelation,
                                    Double tolerance, List<List<Double>> targetItemMeans, List<List<String>> scoringDirections,
                                    Map<Integer, Integer> columnToQuestionMap, Map<Integer, SurveyData> questionDataMap) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> config = new HashMap<>();

            // 基本信息
            config.put("configType", "multiDimensionalScale");
            config.put("description", "分维度调整量表配置");
            config.put("version", "1.0");
            // 使用ISO格式的时间戳
            config.put("timestamp", java.time.Instant.now().toString());

            // 全局参数
            Map<String, Object> globalParams = new HashMap<>();
            globalParams.put("scaleLevel", scaleLevel != null ? scaleLevel : 5);
            globalParams.put("targetTotalAlpha", targetTotalAlpha != null ? targetTotalAlpha : 0.85);
            globalParams.put("targetKMO", targetKMO != null ? targetKMO : 0.8);
            globalParams.put("targetInterDimensionCorrelation", targetInterDimensionCorrelation != null ? targetInterDimensionCorrelation : 0.4);
            globalParams.put("tolerance", tolerance != null ? tolerance : 0.02);
            config.put("globalParams", globalParams);

            // 维度配置
            List<Map<String, Object>> dimensionConfigs = new ArrayList<>();
            for (int i = 0; i < dimensions.size(); i++) {
                Map<String, Object> dimensionConfig = new HashMap<>();
                dimensionConfig.put("name", "维度" + (i + 1));
                dimensionConfig.put("columns", dimensions.get(i));
                dimensionConfig.put("targetAlpha", targetDimensionAlphas != null && i < targetDimensionAlphas.size() ?
                    targetDimensionAlphas.get(i) : 0.8);

                // 题目详细信息
                List<Map<String, Object>> questionDetails = new ArrayList<>();
                List<Integer> dimensionColumns = dimensions.get(i);
                for (int j = 0; j < dimensionColumns.size(); j++) {
                    Integer colIndex = dimensionColumns.get(j);
                    Integer questionId = columnToQuestionMap.get(colIndex);

                    Map<String, Object> questionDetail = new HashMap<>();

                    if (questionId != null && questionDataMap.containsKey(questionId)) {
                        SurveyData questionData = questionDataMap.get(questionId);

                        // 判断是否为矩阵题的子题目
                        if (questionData.getSubQuestions() != null && !questionData.getSubQuestions().isEmpty()) {
                            // 矩阵题：找到对应的子题目索引
                            int subIndex = -1;
                            if (questionData.getColIndices() != null) {
                                for (int k = 0; k < questionData.getColIndices().size(); k++) {
                                    if (questionData.getColIndices().get(k).equals(colIndex)) {
                                        subIndex = k;
                                        break;
                                    }
                                }
                            }

                            questionDetail.put("type", "subquestion");
                            questionDetail.put("mainQuestionId", questionId);
                            questionDetail.put("subIndex", subIndex);
                        } else {
                            // 普通题目
                            questionDetail.put("type", "question");
                            questionDetail.put("questionId", questionId);
                        }
                    } else {
                        // 如果找不到对应的题目，使用默认值
                        questionDetail.put("type", "subquestion");
                        questionDetail.put("mainQuestionId", i + 1);
                        questionDetail.put("subIndex", j);
                    }

                    questionDetail.put("colIndex", colIndex);

                    // 得分方向
                    String scoringDirection = "positive"; // 默认正向
                    if (scoringDirections != null && i < scoringDirections.size() &&
                        j < scoringDirections.get(i).size()) {
                        scoringDirection = scoringDirections.get(i).get(j);
                    }
                    questionDetail.put("scoringDirection", scoringDirection);

                    // 目标均值
                    Object targetMean = null;
                    if (targetItemMeans != null && i < targetItemMeans.size() &&
                        j < targetItemMeans.get(i).size() && targetItemMeans.get(i).get(j) != null) {
                        // 转换为整数（如果是整数值）或保持为小数
                        Double meanValue = targetItemMeans.get(i).get(j);
                        if (meanValue != null) {
                            if (meanValue == meanValue.intValue()) {
                                targetMean = meanValue.intValue();
                            } else {
                                targetMean = meanValue;
                            }
                        }
                    }
                    questionDetail.put("targetMean", targetMean);

                    questionDetails.add(questionDetail);
                }
                dimensionConfig.put("questionDetails", questionDetails);

                dimensionConfigs.add(dimensionConfig);
            }
            config.put("dimensions", dimensionConfigs);

            return objectMapper.writeValueAsString(config);

        } catch (Exception e) {
            log.error("[配置文本生成] 生成失败", e);
            return null;
        }
    }

    /**
     * 验证题目类型是否适用于分维度量表调整
     * @param questionNums 题目编号集合
     * @param questionTypesCache 题目类型缓存
     */
    private void validateQuestionTypesForMultiDimensional(Set<Integer> questionNums, Map<Integer, String> questionTypesCache) {
        // 分维度量表调整只支持特定题型
        Set<String> allowedTypes = Set.of("3", "5", "6single");
        List<String> invalidQuestions = new ArrayList<>();

        for (Integer questionNum : questionNums) {
            String questionType = questionTypesCache.get(questionNum);
            if (questionType == null) {
                log.warn("[分维度量表调整] 题目{}的类型信息缺失，跳过验证", questionNum);
                continue;
            }

            if (!allowedTypes.contains(questionType)) {
                String typeDescription = getQuestionTypeDescription(questionType);
                invalidQuestions.add(String.format("Q%d(%s)", questionNum, typeDescription));
            }
        }

        if (!invalidQuestions.isEmpty()) {
            throw new IllegalArgumentException(String.format(
                "分维度量表调整只支持以下题型：单选题(类型3)、单项量表题(类型5)、矩阵单选题(类型6single)。" +
                "以下题目类型不支持：%s", String.join(", ", invalidQuestions)
            ));
        }

        log.info("[分维度量表调整] 题目类型验证通过，所有题目均为支持的类型");
    }

    /**
     * 验证量表级数一致性
     * @param questionNums 题目编号集合
     * @param questionScaleLevelsMap 题目量表级数映射
     */
    private void validateScaleConsistency(Set<Integer> questionNums, Map<Integer, Integer> questionScaleLevelsMap) {
        Map<Integer, List<Integer>> scaleLevelGroups = new HashMap<>();

        for (Integer questionNum : questionNums) {
            Integer scaleLevel = questionScaleLevelsMap.get(questionNum);
            if (scaleLevel == null) {
                log.warn("[分维度量表调整] 题目{}的量表级数信息缺失，跳过验证", questionNum);
                continue; // 跳过不存在的题目，不参与验证
            }

            scaleLevelGroups.computeIfAbsent(scaleLevel, k -> new ArrayList<>()).add(questionNum);
        }

        if (scaleLevelGroups.size() > 1) {
            List<String> details = new ArrayList<>();
            for (Map.Entry<Integer, List<Integer>> entry : scaleLevelGroups.entrySet()) {
                List<String> questionList = entry.getValue().stream()
                    .map(q -> "Q" + q)
                    .collect(Collectors.toList());
                details.add(String.format("%d级量表: %s", entry.getKey(), String.join(", ", questionList)));
            }

            throw new IllegalArgumentException(String.format(
                "分维度量表调整要求所有题目的量表级数必须一致，但检测到以下不一致：%s。" +
                "建议：请确保所有参与分析的题目使用相同的量表级数（如都是5级量表或都是7级量表）。",
                String.join("；", details)
            ));
        }

        Integer mainScaleLevel = scaleLevelGroups.keySet().iterator().next();
        log.info("[分维度量表调整] 量表级数一致性验证通过，所有题目均为{}级量表", mainScaleLevel);
    }

    /**
     * 获取题目类型描述
     * @param type 题目类型
     * @return 类型描述
     */
    private String getQuestionTypeDescription(String type) {
        Map<String, String> typeMap = Map.of(
            "1", "填空题",
            "2", "填空题",
            "3", "单选题",
            "4", "多选题",
            "5", "单项量表题",
            "6single", "矩阵单选题",
            "6multiple", "矩阵多选题",
            "7", "下拉题",
            "8", "单项滑条题",
            "11", "排序题"
        );
        return typeMap.getOrDefault(type, "未知题型");
    }

}
